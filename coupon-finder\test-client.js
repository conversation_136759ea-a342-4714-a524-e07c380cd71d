/**
 * Test AccessTrade client implementation
 */

import { createAccessTradeClient } from './src/lib/accesstrade-client.js';

const API_KEY = 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV';

async function testClient() {
  console.log('🔍 Testing AccessTrade Client...\n');

  try {
    const client = createAccessTradeClient(API_KEY);
    
    console.log('📡 Testing getCoupons()...');
    const coupons = await client.getCoupons({ 
      merchant: 'shopee',
      status: 1 
    });
    
    console.log(`✅ Success! Got ${coupons.data.length} coupons`);
    
    if (coupons.data.length > 0) {
      const firstCoupon = coupons.data[0];
      console.log('🔍 First coupon:', {
        id: firstCoupon.id,
        name: firstCoupon.name,
        merchant: firstCoupon.merchant,
        domain: firstCoupon.domain,
        categories: firstCoupon.categories.map(c => c.category_name_show),
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testClient();

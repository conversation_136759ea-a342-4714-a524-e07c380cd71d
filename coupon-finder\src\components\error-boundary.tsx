/**
 * Error Boundary Component
 * 
 * React Error Boundary với enhanced error handling:
 * - Custom error display
 * - Retry mechanisms
 * - Error reporting
 * - Integration với TanStack Query error handling
 */

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ApiErrorClass, 
  parseUnknownError, 
  getErrorMessage 
} from '@/lib/api-error-handler';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

/**
 * Default Error Fallback Component
 */
function DefaultErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
  const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="max-w-md w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-red-600">
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            Something went wrong
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm text-gray-600 mb-2">
              {getErrorMessage(parsedError)}
            </p>
            
            {parsedError.code && (
              <p className="text-xs text-gray-400">
                Error Code: {parsedError.code}
              </p>
            )}
          </div>
          
          <div className="flex space-x-3">
            <Button
              onClick={resetErrorBoundary}
              className="flex-1"
            >
              Try Again
            </Button>
            
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="flex-1"
            >
              Reload Page
            </Button>
          </div>
          
          <details className="mt-4">
            <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
              Technical Details
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
              {error.stack}
            </pre>
          </details>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Error Boundary Class Component
 */
export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error
    console.error('Error Boundary Caught:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
    });

    // Report to monitoring service
    // TODO: Integrate với error monitoring (Sentry, LogRocket, etc.)
  }

  resetErrorBoundary = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      
      return (
        <FallbackComponent
          error={this.state.error}
          resetErrorBoundary={this.resetErrorBoundary}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based Error Boundary (using react-error-boundary)
 * Alternative implementation for functional components
 */
export function useErrorHandler() {
  return React.useCallback((error: Error, errorInfo?: React.ErrorInfo) => {
    const parsedError = new ApiErrorClass(parseUnknownError(error));
    
    console.error('Error Handler:', {
      error: parsedError.toJSON(),
      errorInfo,
    });

    // Report error
    // TODO: Integrate với error monitoring
  }, []);
}

/**
 * Higher-order component để wrap components với Error Boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<ErrorFallbackProps>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Query Error Boundary
 * Specialized error boundary cho TanStack Query errors
 */
interface QueryErrorBoundaryProps extends ErrorBoundaryProps {
  resetKeys?: string[];
  resetOnPropsChange?: boolean;
}

export function QueryErrorBoundary({ 
  children, 
  resetKeys = [], 
  resetOnPropsChange = true,
  ...props 
}: QueryErrorBoundaryProps) {
  const [resetCount, setResetCount] = React.useState(0);
  const prevResetKeys = React.useRef(resetKeys);

  // Reset error boundary khi resetKeys thay đổi
  React.useEffect(() => {
    if (resetOnPropsChange && 
        JSON.stringify(prevResetKeys.current) !== JSON.stringify(resetKeys)) {
      prevResetKeys.current = resetKeys;
      setResetCount(count => count + 1);
    }
  }, [resetKeys, resetOnPropsChange]);

  const handleError = React.useCallback((error: Error, errorInfo: React.ErrorInfo) => {
    // Enhanced error logging cho query errors
    console.error('Query Error Boundary:', {
      error: error.message,
      resetKeys,
      resetCount,
      errorInfo,
    });

    if (props.onError) {
      props.onError(error, errorInfo);
    }
  }, [resetKeys, resetCount, props.onError]);

  return (
    <ErrorBoundary
      key={resetCount}
      {...props}
      onError={handleError}
    >
      {children}
    </ErrorBoundary>
  );
}

/**
 * Async Error Boundary
 * Handle async errors trong React components
 */
export function useAsyncError() {
  const [, setError] = React.useState();
  
  return React.useCallback((error: Error) => {
    setError(() => {
      throw error;
    });
  }, []);
}

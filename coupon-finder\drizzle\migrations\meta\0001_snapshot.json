{"version": "6", "dialect": "sqlite", "id": "edd00a1a-cf74-46a6-8354-a763524491b2", "prevId": "3e44d50e-fa4a-4031-949a-5af42c7f5bd3", "tables": {"admin_settings": {"name": "admin_settings", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"admin_settings_key_unique": {"name": "admin_settings_key_unique", "columns": ["key"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "analytics": {"name": "analytics", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "affiliate_link": {"name": "affiliate_link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_analytics_event_type": {"name": "idx_analytics_event_type", "columns": ["event_type"], "isUnique": false}, "idx_analytics_created_at": {"name": "idx_analytics_created_at", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {"analytics_user_id_users_id_fk": {"name": "analytics_user_id_users_id_fk", "tableFrom": "analytics", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "categories": {"name": "categories", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"categories_slug_unique": {"name": "categories_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "featured_products": {"name": "featured_products", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "product_id": {"name": "product_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "affiliate_link": {"name": "affiliate_link", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category_id": {"name": "category_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_featured_products_category": {"name": "idx_featured_products_category", "columns": ["category_id"], "isUnique": false}, "idx_featured_products_active": {"name": "idx_featured_products_active", "columns": ["is_active"], "isUnique": false}}, "foreignKeys": {"featured_products_category_id_categories_id_fk": {"name": "featured_products_category_id_categories_id_fk", "tableFrom": "featured_products", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_sessions_user": {"name": "idx_sessions_user", "columns": ["user_id"], "isUnique": false}, "idx_sessions_token": {"name": "idx_sessions_token", "columns": ["token"], "isUnique": false}, "idx_sessions_expires": {"name": "idx_sessions_expires", "columns": ["expires_at"], "isUnique": false}}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'user'"}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "last_login_at": {"name": "last_login_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}
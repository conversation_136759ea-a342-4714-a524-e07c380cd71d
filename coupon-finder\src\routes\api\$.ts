/**
 * TanStack Start API route that bridges to Hono application
 *
 * This catch-all route handles all API requests and forwards them
 * to the Hono application through the bridge layer.
 */

import { createAPIFileRoute } from '@tanstack/react-start/api'
import honoApp from '../../api/hono-app'
import { createApiHandler } from '../../api/bridge'

// Create the bridge handler with enhanced configuration
const apiHandler = createApiHandler(honoApp, {
  basePath: '/api',
  timeout: 30000,
  enableLogging: true,
  enableMetrics: true,
  transformRequest: async (request) => {
    // Add any custom request transformations here
    // For example, add custom headers or modify the request
    return request
  },
  transformResponse: async (response) => {
    // Add any custom response transformations here
    // For example, add custom headers or modify the response
    const headers = new Headers(response.headers)

    // Add API version header
    headers.set('X-API-Version', '1.0.0')

    // Add server information
    headers.set('X-Powered-By', 'TanStack Start + Hono')

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    })
  },
})

/**
 * Handle all HTTP methods through the bridge
 */
export const APIRoute = createAPIFileRoute('/api/$')({
  GET: async ({ request }) => {
    return apiHandler(request)
  },
  POST: async ({ request }) => {
    return apiHandler(request)
  },
  PUT: async ({ request }) => {
    return apiHandler(request)
  },
  DELETE: async ({ request }) => {
    return apiHandler(request)
  },
  PATCH: async ({ request }) => {
    return apiHandler(request)
  },
  OPTIONS: async ({ request }) => {
    return apiHandler(request)
  },
  HEAD: async ({ request }) => {
    return apiHandler(request)
  },
})

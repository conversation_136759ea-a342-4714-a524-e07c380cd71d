import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { api } from '../api/hono-client'

// Types cho Coupons Store
export interface Coupon {
  id: string
  code: string
  title: string
  description: string
  discount: number
  discountType: 'percentage' | 'fixed'
  minOrderValue?: number
  maxDiscount?: number
  merchant: string
  category: string
  imageUrl?: string
  affiliateLink: string
  expiryDate: string
  isActive: boolean
  usageCount: number
  maxUsage?: number
  createdAt: string
}

export interface CouponFilters {
  category?: string
  merchant?: string
  minDiscount?: number
  discountType?: 'percentage' | 'fixed'
  isActive?: boolean
  sortBy?: 'discount' | 'expiry' | 'newest' | 'popular'
  sortOrder?: 'asc' | 'desc'
}

export interface CouponsState {
  // State
  coupons: Coupon[]
  featuredCoupons: Coupon[]
  categoryCoupons: Record<string, Coupon[]>
  searchQuery: string
  filters: CouponFilters
  selectedCategory: string
  isLoading: boolean
  error: string | null
  
  // Pagination
  currentPage: number
  totalPages: number
  totalCoupons: number
  
  // Recently used coupons
  recentlyUsed: string[] // coupon IDs
  
  // Actions
  setCoupons: (coupons: Coupon[]) => void
  setFeaturedCoupons: (coupons: Coupon[]) => void
  setCategoryCoupons: (category: string, coupons: Coupon[]) => void
  setSearchQuery: (query: string) => void
  setFilters: (filters: Partial<CouponFilters>) => void
  clearFilters: () => void
  setSelectedCategory: (category: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setPagination: (page: number, totalPages: number, totalCoupons: number) => void
  addToRecentlyUsed: (couponId: string) => void
  
  // API Actions
  searchCoupons: (query: string, filters?: CouponFilters) => Promise<void>
  fetchCouponsByCategory: (category: string) => Promise<void>
  fetchFeaturedCoupons: () => Promise<void>
  fetchCouponByUrl: (url: string) => Promise<void>
  
  // Computed
  getFilteredCoupons: () => Coupon[]
  getActiveCoupons: () => Coupon[]
  getCouponsByMerchant: (merchant: string) => Coupon[]
  getRecentlyUsedCoupons: () => Coupon[]
}

// Coupons Store
export const useCouponsStore = create<CouponsState>()(
  persist(
    (set, get) => ({
      // Initial state
      coupons: [],
      featuredCoupons: [],
      categoryCoupons: {},
      searchQuery: '',
      filters: {},
      selectedCategory: 'all',
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 1,
      totalCoupons: 0,
      recentlyUsed: [],

      // Actions
      setCoupons: (coupons: Coupon[]) => {
        set({ coupons })
      },

      setFeaturedCoupons: (coupons: Coupon[]) => {
        set({ featuredCoupons: coupons })
      },

      setCategoryCoupons: (category: string, coupons: Coupon[]) => {
        const { categoryCoupons } = get()
        set({
          categoryCoupons: {
            ...categoryCoupons,
            [category]: coupons
          }
        })
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      setFilters: (newFilters: Partial<CouponFilters>) => {
        const { filters } = get()
        set({
          filters: { ...filters, ...newFilters }
        })
      },

      clearFilters: () => {
        set({ filters: {} })
      },

      setSelectedCategory: (category: string) => {
        set({ selectedCategory: category })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setError: (error: string | null) => {
        set({ error })
      },

      setPagination: (page: number, totalPages: number, totalCoupons: number) => {
        set({
          currentPage: page,
          totalPages,
          totalCoupons
        })
      },

      addToRecentlyUsed: (couponId: string) => {
        const { recentlyUsed } = get()
        const updated = [couponId, ...recentlyUsed.filter(id => id !== couponId)].slice(0, 10)
        set({ recentlyUsed: updated })
      },

      // API Actions
      searchCoupons: async (query: string, filters?: CouponFilters) => {
        set({ isLoading: true, error: null })

        try {
          // Use Hono RPC client
          const result = await api.coupons.search({
            keyword: query,
            category: filters?.category,
            merchant: filters?.merchant,
            limit: 20,
            offset: 0,
          })

          if (!result.success) {
            throw new Error(result.message || 'Failed to search coupons')
          }

          const { data } = result

          set({
            coupons: data.coupons || [],
            searchQuery: query,
            filters: filters || {},
            currentPage: 1,
            totalPages: Math.ceil((data.total || 0) / 20),
            totalCoupons: data.total || 0,
            isLoading: false,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Search failed',
          })
        }
      },

      fetchCouponsByCategory: async (category: string) => {
        set({ isLoading: true, error: null })

        try {
          // Use Hono RPC client instead of fetch
          const { api } = await import('../api/hono-client')
          const result = await api.coupons.getByCategory(category)

          if (!result.success) {
            throw new Error(result.message || 'Failed to fetch coupons')
          }

          const coupons = result.data.coupons || []

          set(state => ({
            categoryCoupons: {
              ...state.categoryCoupons,
              [category]: coupons
            },
            isLoading: false,
          }))
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch coupons',
          })
        }
      },

      fetchFeaturedCoupons: async () => {
        try {
          // Use Hono RPC client - Get hot/featured coupons from AccessTrade
          const result = await api.coupons.getHot({ limit: 10, date: 1 })
          const coupons = result.success ? result.data.coupons || [] : []
          set({ featuredCoupons: coupons })
        } catch (error) {
          console.error('Failed to fetch featured coupons:', error)
        }
      },

      fetchCouponByUrl: async (url: string) => {
        set({ isLoading: true, error: null })

        try {
          // Use Hono RPC client
          const result = await api.coupons.search({
            url: url,
            limit: 20,
          })

          if (!result.success) {
            throw new Error(result.message || 'Failed to find coupons for URL')
          }

          const coupons = result.data.coupons || []

          set({
            coupons,
            isLoading: false,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to find coupons',
          })
        }
      },

      // Computed properties
      getFilteredCoupons: () => {
        const { coupons, filters } = get()
        let filtered = [...coupons]

        // Apply filters
        if (filters.category) {
          filtered = filtered.filter(c => c.category === filters.category)
        }
        if (filters.merchant) {
          filtered = filtered.filter(c => c.merchant === filters.merchant)
        }
        if (filters.minDiscount) {
          filtered = filtered.filter(c => c.discount >= filters.minDiscount!)
        }
        if (filters.discountType) {
          filtered = filtered.filter(c => c.discountType === filters.discountType)
        }
        if (filters.isActive !== undefined) {
          filtered = filtered.filter(c => c.isActive === filters.isActive)
        }

        // Apply sorting
        if (filters.sortBy) {
          filtered.sort((a, b) => {
            const order = filters.sortOrder === 'desc' ? -1 : 1
            switch (filters.sortBy) {
              case 'discount':
                return (a.discount - b.discount) * order
              case 'expiry':
                return (new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime()) * order
              case 'newest':
                return (new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()) * order
              case 'popular':
                return (a.usageCount - b.usageCount) * order
              default:
                return 0
            }
          })
        }

        return filtered
      },

      getActiveCoupons: () => {
        const { coupons } = get()
        return coupons.filter(c => c.isActive && new Date(c.expiryDate) > new Date())
      },

      getCouponsByMerchant: (merchant: string) => {
        const { coupons } = get()
        return coupons.filter(c => c.merchant === merchant)
      },

      getRecentlyUsedCoupons: () => {
        const { coupons, recentlyUsed } = get()
        return recentlyUsed
          .map(id => coupons.find(c => c.id === id))
          .filter(Boolean) as Coupon[]
      },
    }),
    {
      name: 'coupons-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        recentlyUsed: state.recentlyUsed,
        filters: state.filters,
        selectedCategory: state.selectedCategory,
      }),
    }
  )
)

// Selectors cho performance optimization
export const useCoupons = () => useCouponsStore((state) => state.coupons)
export const useFeaturedCoupons = () => useCouponsStore((state) => state.featuredCoupons)
export const useCategoryCoupons = () => useCouponsStore((state) => state.categoryCoupons)
export const useCouponsLoading = () => useCouponsStore((state) => state.isLoading)
export const useCouponsError = () => useCouponsStore((state) => state.error)
export const useCouponFilters = () => useCouponsStore((state) => state.filters)
export const useSelectedCategory = () => useCouponsStore((state) => state.selectedCategory)
export const useRecentlyUsedCoupons = () => useCouponsStore((state) => state.getRecentlyUsedCoupons())

// Actions selectors
export const useCouponsActions = () => useCouponsStore((state) => ({
  setCoupons: state.setCoupons,
  setFilters: state.setFilters,
  clearFilters: state.clearFilters,
  setSelectedCategory: state.setSelectedCategory,
  addToRecentlyUsed: state.addToRecentlyUsed,
  searchCoupons: state.searchCoupons,
  fetchCouponsByCategory: state.fetchCouponsByCategory,
  fetchFeaturedCoupons: state.fetchFeaturedCoupons,
  fetchCouponByUrl: state.fetchCouponByUrl,
}))

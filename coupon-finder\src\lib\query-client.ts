import { QueryClient } from '@tanstack/react-query';
import {
  ApiErrorClass,
  parseUnknownError,
  isAuthError,
  isNetworkError
} from './api-error-handler';
import {
  createGlobalErrorHandler,
  createGlobalMutationErrorHandler
} from './query-error-handler';

/**
 * Tạo QueryClient instance với configuration tối ưu cho TanStack Start
 *
 * Configuration này được thiết kế để:
 * - Tối ưu cho SSR với TanStack Start
 * - Tr<PERSON>h refetch ngay lập tức sau hydration
 * - Cung cấp error handling tốt với custom error classes
 * - Tích hợp với Cloudflare Workers
 * - Global error handling cho queries và mutations
 */
export function createQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Với SSR, chúng ta muốn set staleTime > 0 để tránh refetch ngay lập tức
        // sau khi client hydrate
        staleTime: 60 * 1000, // 1 phút

        // Thời gian cache data trong memory trước khi garbage collect
        gcTime: 5 * 60 * 1000, // 5 phút

        // Enhanced retry logic với custom error handling
        retry: (failureCount, error: any) => {
          const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));

          // Không retry cho auth errors
          if (isAuthError(parsedError)) {
            return false;
          }

          // Không retry cho 4xx errors (client errors) trừ 408, 429
          if (parsedError.status >= 400 && parsedError.status < 500) {
            // Retry cho timeout và rate limit
            if (parsedError.status === 408 || parsedError.status === 429) {
              return failureCount < 2;
            }
            return false;
          }

          // Retry cho network errors
          if (isNetworkError(parsedError)) {
            return failureCount < 3;
          }

          // Retry tối đa 3 lần cho server errors
          return failureCount < 3;
        },

        // Enhanced retry delay với exponential backoff
        retryDelay: (attemptIndex, error) => {
          const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));

          // Longer delay cho rate limit errors
          if (parsedError.status === 429) {
            return Math.min(5000 * 2 ** attemptIndex, 60000);
          }

          // Standard exponential backoff
          return Math.min(1000 * 2 ** attemptIndex, 30000);
        },

        // Refetch khi window focus (tốt cho UX)
        refetchOnWindowFocus: true,

        // Refetch khi reconnect network
        refetchOnReconnect: true,

        // Không refetch khi mount nếu data vẫn fresh
        refetchOnMount: true,

        // Global error handler cho queries
        ...createGlobalErrorHandler(),
      },
      mutations: {
        // Enhanced retry cho mutations
        retry: (failureCount, error: any) => {
          const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));

          // Không retry cho auth errors và validation errors
          if (isAuthError(parsedError) || parsedError.status === 400) {
            return false;
          }

          // Retry 1 lần cho network errors và server errors
          return failureCount < 1;
        },

        // Retry delay cho mutations
        retryDelay: 1000,

        // Global error handler cho mutations
        ...createGlobalMutationErrorHandler(),
      },
    },
  });
}

/**
 * Type-safe query keys factory
 * Giúp tạo query keys consistent và type-safe
 */
export const queryKeys = {
  // Auth queries
  auth: {
    user: () => ['auth', 'user'] as const,
    session: () => ['auth', 'session'] as const,
  },
  
  // Coupon queries
  coupons: {
    all: () => ['coupons'] as const,
    lists: () => [...queryKeys.coupons.all(), 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.coupons.lists(), filters] as const,
    details: () => [...queryKeys.coupons.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.coupons.details(), id] as const,
    search: (query: string) => [...queryKeys.coupons.all(), 'search', query] as const,
    categories: () => [...queryKeys.coupons.all(), 'categories'] as const,
    category: (categoryId: string) => [...queryKeys.coupons.categories(), categoryId] as const,
  },
  
  // Product queries
  products: {
    all: () => ['products'] as const,
    lists: () => [...queryKeys.products.all(), 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.products.lists(), filters] as const,
    details: () => [...queryKeys.products.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.products.details(), id] as const,
    search: (query: string) => [...queryKeys.products.all(), 'search', query] as const,
    compare: (productIds: string[]) => [...queryKeys.products.all(), 'compare', productIds] as const,
    topSelling: () => [...queryKeys.products.all(), 'top-selling'] as const,
  },
  
  // Campaign queries
  campaigns: {
    all: () => ['campaigns'] as const,
    lists: () => [...queryKeys.campaigns.all(), 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.campaigns.lists(), filters] as const,
    details: () => [...queryKeys.campaigns.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.campaigns.details(), id] as const,
    active: () => [...queryKeys.campaigns.all(), 'active'] as const,
  },
  
  // Admin queries
  admin: {
    all: () => ['admin'] as const,
    analytics: () => [...queryKeys.admin.all(), 'analytics'] as const,
    users: () => [...queryKeys.admin.all(), 'users'] as const,
    settings: () => [...queryKeys.admin.all(), 'settings'] as const,
  },
} as const;

/**
 * Query options factory cho common queries
 * Giúp tạo query options reusable và type-safe
 */
export const queryOptions = {
  // Auth options
  auth: {
    user: () => ({
      queryKey: queryKeys.auth.user(),
      queryFn: async () => {
        const response = await fetch('/api/auth/me');
        if (!response.ok) {
          throw new Error('Failed to fetch user');
        }
        return response.json();
      },
      staleTime: 5 * 60 * 1000, // 5 phút
    }),
  },
  
  // Coupon options
  coupons: {
    list: (filters: Record<string, any> = {}) => ({
      queryKey: queryKeys.coupons.list(filters),
      queryFn: async () => {
        // Use Hono RPC client instead of fetch
        const { api } = await import('../api/hono-client');
        return api.coupons.search({
          keyword: filters.keyword,
          category: filters.category,
          merchant: filters.merchant,
          limit: filters.limit ? parseInt(filters.limit) : 20,
          offset: filters.offset ? parseInt(filters.offset) : 0,
        });
      },
      staleTime: 2 * 60 * 1000, // 2 phút
    }),

    search: (query: string) => ({
      queryKey: queryKeys.coupons.search(query),
      queryFn: async () => {
        // Use Hono RPC client instead of fetch
        const { api } = await import('../api/hono-client');
        return api.coupons.search({
          keyword: query,
          limit: 20,
        });
      },
      enabled: !!query && query.length > 2, // Chỉ search khi có ít nhất 3 ký tự
      staleTime: 30 * 1000, // 30 giây
    }),
  },
  
  // Product options
  products: {
    search: (query: string) => ({
      queryKey: queryKeys.products.search(query),
      queryFn: async () => {
        // Use Hono RPC client instead of fetch
        const { api } = await import('../api/hono-client');
        return api.products.search({
          keyword: query,
          limit: 20,
        });
      },
      enabled: !!query && query.length > 2,
      staleTime: 30 * 1000,
    }),

    compare: (productIds: string[]) => ({
      queryKey: queryKeys.products.compare(productIds),
      queryFn: async () => {
        // Use Hono RPC client instead of fetch
        const { api } = await import('../api/hono-client');
        return api.products.compare(productIds);
      },
      enabled: productIds.length > 0,
      staleTime: 5 * 60 * 1000,
    }),
  },
} as const;

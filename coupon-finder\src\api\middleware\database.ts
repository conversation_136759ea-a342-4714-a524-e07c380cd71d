import { Context, Next } from 'hono'
import { drizzle } from 'drizzle-orm/d1'
import { DatabaseError } from './error-handler'
import * as schema from '../../db/schema'

/**
 * Database health check interface
 */
export interface DatabaseHealth {
  healthy: boolean
  latency?: number
  error?: string
  timestamp: string
}

/**
 * Database metrics interface
 */
export interface DatabaseMetrics {
  connections: number
  queries: number
  errors: number
  avgLatency: number
  lastQuery: string
}

/**
 * Database connection cache
 */
const dbCache = new Map<string, any>()

/**
 * Create database instance with caching
 */
export function createDb(database: D1Database, cacheKey: string = 'default') {
  if (dbCache.has(cacheKey)) {
    return dbCache.get(cacheKey)
  }

  const db = drizzle(database, { schema })
  dbCache.set(cacheKey, db)
  return db
}

/**
 * Database middleware - provides database instance in context
 */
export function databaseMiddleware() {
  return async (c: Context, next: Next) => {
    try {
      if (!c.env.DB) {
        throw new DatabaseError('Database binding not found', 'connection', 'D1')
      }

      // Create database instance with caching
      const requestId = c.get('requestId') || 'default'
      const db = createDb(c.env.DB, requestId)
      
      // Store database instance in context
      c.set('db', db)
      c.set('rawDb', c.env.DB)
      
      await next()
    } catch (error) {
      if (error instanceof DatabaseError) {
        throw error
      }
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Database connection failed',
        'connection'
      )
    }
  }
}

/**
 * Database health check middleware
 */
export function dbHealthMiddleware() {
  return async (c: Context, next: Next) => {
    const startTime = Date.now()
    let health: DatabaseHealth

    try {
      const rawDb = c.get('rawDb') as D1Database
      if (!rawDb) {
        health = {
          healthy: false,
          error: 'Database not available',
          timestamp: new Date().toISOString(),
        }
      } else {
        // Simple health check query
        await rawDb.prepare('SELECT 1').first()
        const latency = Date.now() - startTime
        
        health = {
          healthy: true,
          latency,
          timestamp: new Date().toISOString(),
        }
      }
    } catch (error) {
      health = {
        healthy: false,
        error: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString(),
      }
    }

    // Store health info in context
    c.set('dbHealth', health)
    
    // Add health info to response headers for monitoring
    c.res.headers.set('X-DB-Health', health.healthy ? 'ok' : 'error')
    if (health.latency) {
      c.res.headers.set('X-DB-Latency', health.latency.toString())
    }

    await next()
  }
}

/**
 * Database metrics middleware
 */
export function dbMetricsMiddleware() {
  const metrics: DatabaseMetrics = {
    connections: 0,
    queries: 0,
    errors: 0,
    avgLatency: 0,
    lastQuery: '',
  }

  return async (c: Context, next: Next) => {
    const startTime = Date.now()
    metrics.connections++

    try {
      await next()
      
      const latency = Date.now() - startTime
      metrics.queries++
      metrics.avgLatency = (metrics.avgLatency + latency) / 2
      metrics.lastQuery = new Date().toISOString()
      
    } catch (error) {
      metrics.errors++
      throw error
    } finally {
      // Store metrics in context
      c.set('dbMetrics', { ...metrics })
      
      // Add metrics to response headers
      c.res.headers.set('X-DB-Queries', metrics.queries.toString())
      c.res.headers.set('X-DB-Errors', metrics.errors.toString())
    }
  }
}

/**
 * Database transaction middleware
 */
export function dbTransactionMiddleware() {
  return async (c: Context, next: Next) => {
    const db = c.get('db')
    if (!db) {
      throw new DatabaseError('Database not available', 'transaction')
    }

    // For D1, transactions are not yet fully supported
    // This is a placeholder for future transaction support
    const transaction = {
      async run<T>(callback: () => Promise<T>): Promise<T> {
        try {
          return await callback()
        } catch (error) {
          throw new DatabaseError(
            error instanceof Error ? error.message : 'Transaction failed',
            'transaction'
          )
        }
      }
    }

    c.set('transaction', transaction)
    await next()
  }
}

/**
 * Database query timeout middleware
 */
export function dbTimeoutMiddleware(timeoutMs: number = 10000) {
  return async (c: Context, next: Next) => {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new DatabaseError('Database query timeout', 'timeout'))
      }, timeoutMs)
    })

    try {
      await Promise.race([next(), timeoutPromise])
    } catch (error) {
      throw error
    }
  }
}

/**
 * Database connection pool middleware (for future use)
 */
export function dbPoolMiddleware(options?: {
  maxConnections?: number
  idleTimeout?: number
}) {
  const { maxConnections = 10, idleTimeout = 30000 } = options || {}
  const pool = new Map<string, { db: any; lastUsed: number }>()

  return async (c: Context, next: Next) => {
    const requestId = c.get('requestId') || 'default'
    
    // Clean up idle connections
    const now = Date.now()
    for (const [key, connection] of pool.entries()) {
      if (now - connection.lastUsed > idleTimeout) {
        pool.delete(key)
      }
    }

    // Check pool size
    if (pool.size >= maxConnections) {
      throw new DatabaseError('Connection pool exhausted', 'pool')
    }

    // Get or create connection
    let connection = pool.get(requestId)
    if (!connection) {
      const db = createDb(c.env.DB, requestId)
      connection = { db, lastUsed: now }
      pool.set(requestId, connection)
    } else {
      connection.lastUsed = now
    }

    c.set('db', connection.db)
    await next()
  }
}

/**
 * Database migration check middleware
 */
export function dbMigrationMiddleware() {
  return async (c: Context, next: Next) => {
    try {
      const rawDb = c.get('rawDb') as D1Database
      if (!rawDb) {
        throw new DatabaseError('Database not available', 'migration')
      }

      // Check if required tables exist
      const tables = ['users', 'categories', 'featuredProducts', 'analytics', 'adminSettings']
      
      for (const table of tables) {
        try {
          await rawDb.prepare(`SELECT 1 FROM ${table} LIMIT 1`).first()
        } catch (error) {
          console.warn(`Table ${table} might not exist or be accessible`)
        }
      }

      await next()
    } catch (error) {
      throw new DatabaseError(
        error instanceof Error ? error.message : 'Migration check failed',
        'migration'
      )
    }
  }
}

/**
 * Database backup middleware (for critical operations)
 */
export function dbBackupMiddleware() {
  return async (c: Context, next: Next) => {
    const method = c.req.method.toUpperCase()
    const isCriticalOperation = ['POST', 'PUT', 'DELETE'].includes(method)

    if (isCriticalOperation) {
      // Log critical operations for backup/audit purposes
      console.info('Critical database operation:', {
        method,
        path: c.req.path,
        requestId: c.get('requestId'),
        userId: c.get('userId'),
        timestamp: new Date().toISOString(),
      })
    }

    await next()
  }
}

/**
 * Utility function to check database health
 */
export async function checkDbHealth(database: D1Database): Promise<DatabaseHealth> {
  const startTime = Date.now()
  
  try {
    await database.prepare('SELECT 1').first()
    const latency = Date.now() - startTime
    
    return {
      healthy: true,
      latency,
      timestamp: new Date().toISOString(),
    }
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Health check failed',
      timestamp: new Date().toISOString(),
    }
  }
}

/**
 * Utility function to run migrations
 */
export async function runMigrations(database: D1Database): Promise<void> {
  try {
    // This is a placeholder for migration logic
    // In production, implement proper migration system
    console.info('Migration check completed')
  } catch (error) {
    throw new DatabaseError(
      error instanceof Error ? error.message : 'Migration failed',
      'migration'
    )
  }
}

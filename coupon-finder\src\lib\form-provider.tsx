/**
 * TanStack Form Provider
 * 
 * Global form configuration và utilities cho toàn bộ ứng dụng
 * Tích hợp với Zod v4 validation và error handling
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import { z } from 'zod/v4';
import type { FormApi } from '@tanstack/react-form';

// ===== TYPE DEFINITIONS =====

export interface FormNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

export interface FormContextValue {
  // Global form state
  isAnyFormSubmitting: boolean;
  
  // Notification system
  notifications: FormNotification[];
  addNotification: (notification: Omit<FormNotification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Form registration
  registerForm: (formId: string, formApi: FormApi<any, any>) => void;
  unregisterForm: (formId: string) => void;
  getForm: (formId: string) => FormApi<any, any> | undefined;
  
  // Global form actions
  resetAllForms: () => void;
  validateAllForms: () => Promise<boolean>;
  
  // Error handling
  handleFormError: (error: unknown, formId?: string) => void;
  
  // Configuration
  config: FormProviderConfig;
  updateConfig: (config: Partial<FormProviderConfig>) => void;
}

export interface FormProviderConfig {
  // Default validation settings
  defaultAsyncDebounceMs: number;
  defaultValidationTrigger: 'onChange' | 'onBlur' | 'onSubmit';
  
  // Error handling
  showErrorNotifications: boolean;
  showSuccessNotifications: boolean;
  defaultNotificationDuration: number;
  
  // Development settings
  enableFormDebug: boolean;
  logFormSubmissions: boolean;
  
  // Zod settings
  zodErrorFormat: 'first' | 'all';
  customErrorMessages: Record<string, string>;
}

// ===== DEFAULT CONFIG =====

const DEFAULT_CONFIG: FormProviderConfig = {
  defaultAsyncDebounceMs: 300,
  defaultValidationTrigger: 'onChange',
  showErrorNotifications: true,
  showSuccessNotifications: true,
  defaultNotificationDuration: 5000,
  enableFormDebug: process.env.NODE_ENV === 'development',
  logFormSubmissions: process.env.NODE_ENV === 'development',
  zodErrorFormat: 'first',
  customErrorMessages: {
    'invalid_type': 'Kiểu dữ liệu không hợp lệ',
    'required': 'Trường này là bắt buộc',
    'too_small': 'Giá trị quá nhỏ',
    'too_big': 'Giá trị quá lớn',
    'invalid_string': 'Chuỗi không hợp lệ',
    'invalid_email': 'Email không hợp lệ',
    'invalid_url': 'URL không hợp lệ'
  }
};

// ===== CONTEXT =====

const FormContext = createContext<FormContextValue | null>(null);

// ===== PROVIDER COMPONENT =====

export interface FormProviderProps {
  children: React.ReactNode;
  config?: Partial<FormProviderConfig>;
}

export function FormProvider({ children, config: userConfig = {} }: FormProviderProps) {
  // State
  const [config, setConfig] = useState<FormProviderConfig>({
    ...DEFAULT_CONFIG,
    ...userConfig
  });
  
  const [notifications, setNotifications] = useState<FormNotification[]>([]);
  const [registeredForms, setRegisteredForms] = useState<Map<string, FormApi<any, any>>>(new Map());

  // Computed values
  const isAnyFormSubmitting = Array.from(registeredForms.values()).some(
    form => form.state.isSubmitting
  );

  // Notification methods
  const addNotification = useCallback((notification: Omit<FormNotification, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: FormNotification = {
      ...notification,
      id,
      duration: notification.duration ?? config.defaultNotificationDuration
    };
    
    setNotifications(prev => [...prev, newNotification]);
    
    // Auto remove notification
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }
  }, [config.defaultNotificationDuration]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Form registration methods
  const registerForm = useCallback((formId: string, formApi: FormApi<any, any>) => {
    setRegisteredForms(prev => new Map(prev).set(formId, formApi));
    
    if (config.enableFormDebug) {
      console.log(`[FormProvider] Registered form: ${formId}`);
    }
  }, [config.enableFormDebug]);

  const unregisterForm = useCallback((formId: string) => {
    setRegisteredForms(prev => {
      const newMap = new Map(prev);
      newMap.delete(formId);
      return newMap;
    });
    
    if (config.enableFormDebug) {
      console.log(`[FormProvider] Unregistered form: ${formId}`);
    }
  }, [config.enableFormDebug]);

  const getForm = useCallback((formId: string) => {
    return registeredForms.get(formId);
  }, [registeredForms]);

  // Global form actions
  const resetAllForms = useCallback(() => {
    registeredForms.forEach((form, formId) => {
      form.reset();
      if (config.enableFormDebug) {
        console.log(`[FormProvider] Reset form: ${formId}`);
      }
    });
  }, [registeredForms, config.enableFormDebug]);

  const validateAllForms = useCallback(async () => {
    const validationPromises = Array.from(registeredForms.entries()).map(
      async ([formId, form]) => {
        try {
          await form.validateAllFields('submit');
          return { formId, isValid: form.state.isValid };
        } catch (error) {
          if (config.enableFormDebug) {
            console.error(`[FormProvider] Validation error in form ${formId}:`, error);
          }
          return { formId, isValid: false };
        }
      }
    );

    const results = await Promise.all(validationPromises);
    const allValid = results.every(result => result.isValid);
    
    if (config.enableFormDebug) {
      console.log('[FormProvider] Global validation results:', results);
    }
    
    return allValid;
  }, [registeredForms, config.enableFormDebug]);

  // Error handling
  const handleFormError = useCallback((error: unknown, formId?: string) => {
    let errorMessage = 'Có lỗi xảy ra';
    let errorDetails: string[] = [];

    if (error instanceof z.ZodError) {
      // Handle Zod validation errors
      if (config.zodErrorFormat === 'first') {
        errorMessage = error.errors[0]?.message || errorMessage;
      } else {
        errorDetails = error.errors.map(err => err.message);
        errorMessage = `Có ${errorDetails.length} lỗi validation`;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    if (config.showErrorNotifications) {
      addNotification({
        type: 'error',
        title: formId ? `Lỗi form ${formId}` : 'Lỗi form',
        message: errorDetails.length > 0 ? errorDetails.join(', ') : errorMessage
      });
    }

    if (config.enableFormDebug) {
      console.error(`[FormProvider] Form error${formId ? ` in ${formId}` : ''}:`, error);
    }
  }, [config, addNotification]);

  // Config update
  const updateConfig = useCallback((newConfig: Partial<FormProviderConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  // Context value
  const contextValue: FormContextValue = {
    isAnyFormSubmitting,
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    registerForm,
    unregisterForm,
    getForm,
    resetAllForms,
    validateAllForms,
    handleFormError,
    config,
    updateConfig
  };

  return (
    <FormContext.Provider value={contextValue}>
      {children}
    </FormContext.Provider>
  );
}

// ===== HOOKS =====

/**
 * Hook để sử dụng Form context
 */
export function useFormContext(): FormContextValue {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
}

/**
 * Hook để register form với global context
 */
export function useFormRegistration(formId: string, formApi: FormApi<any, any>) {
  const { registerForm, unregisterForm } = useFormContext();

  React.useEffect(() => {
    registerForm(formId, formApi);
    return () => unregisterForm(formId);
  }, [formId, formApi, registerForm, unregisterForm]);
}

/**
 * Hook để handle form notifications
 */
export function useFormNotifications() {
  const { notifications, addNotification, removeNotification, clearNotifications } = useFormContext();
  
  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    // Convenience methods
    showSuccess: (title: string, message?: string) => 
      addNotification({ type: 'success', title, message }),
    showError: (title: string, message?: string) => 
      addNotification({ type: 'error', title, message }),
    showWarning: (title: string, message?: string) => 
      addNotification({ type: 'warning', title, message }),
    showInfo: (title: string, message?: string) => 
      addNotification({ type: 'info', title, message })
  };
}

/**
 * Hook để access global form actions
 */
export function useGlobalFormActions() {
  const { 
    resetAllForms, 
    validateAllForms, 
    isAnyFormSubmitting,
    getForm 
  } = useFormContext();
  
  return {
    resetAllForms,
    validateAllForms,
    isAnyFormSubmitting,
    getForm
  };
}

// ===== UTILITIES =====

/**
 * Utility để format Zod errors theo config
 */
export function formatZodError(error: z.ZodError, config: FormProviderConfig): string {
  if (config.zodErrorFormat === 'first') {
    const firstError = error.errors[0];
    return config.customErrorMessages[firstError?.code] || firstError?.message || 'Validation error';
  } else {
    return error.errors
      .map(err => config.customErrorMessages[err.code] || err.message)
      .join(', ');
  }
}

/**
 * Utility để create enhanced form submission handler
 */
export function createFormSubmissionHandler<T>(
  handler: (data: T) => Promise<any>,
  options: {
    formId?: string;
    successMessage?: string;
    errorMessage?: string;
    showNotifications?: boolean;
  } = {}
) {
  return async (data: T) => {
    const { handleFormError, addNotification, config } = useFormContext();
    
    try {
      const result = await handler(data);
      
      if (options.showNotifications !== false && config.showSuccessNotifications && options.successMessage) {
        addNotification({
          type: 'success',
          title: options.successMessage
        });
      }
      
      if (config.logFormSubmissions) {
        console.log(`[FormProvider] Form submission success${options.formId ? ` (${options.formId})` : ''}:`, data);
      }
      
      return result;
    } catch (error) {
      handleFormError(error, options.formId);
      
      if (config.logFormSubmissions) {
        console.error(`[FormProvider] Form submission error${options.formId ? ` (${options.formId})` : ''}:`, error);
      }
      
      throw error;
    }
  };
}

import { createFileRoute, redirect, useNavigate, useSearch } from '@tanstack/react-router'
import { useState } from 'react'
import { useAuth } from '@/lib/auth-context'
import { LoginForm } from '@/components/forms/login-form'
import type { LoginFormData } from '@/lib/validation-schemas'
import type { FormSubmissionResult } from '@/lib/form-utils'

export const Route = createFileRoute('/login')({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      redirect: (search.redirect as string) || '/',
    }
  },
  component: LoginPage,
})

function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)

  const { login, register, isAuthenticated } = useAuth()
  const navigate = useNavigate()
  const search = useSearch({ from: '/login' })

  // Redirect if already authenticated
  if (isAuthenticated) {
    window.location.href = search.redirect;
    return null;
  }

  const handleLoginSubmit = async (data: LoginFormData): Promise<FormSubmissionResult<LoginFormData>> => {
    setIsLoading(true)

    try {
      const result = await login(data.email, data.password)

      if (result.success) {
        navigate({ to: search.redirect })
        return {
          success: true,
          data,
          message: 'Đăng nhập thành công!'
        }
      } else {
        return {
          success: false,
          errors: {
            email: result.error || 'Email hoặc mật khẩu không đúng'
          },
          message: 'Đăng nhập thất bại'
        }
      }
    } catch (error) {
      return {
        success: false,
        message: 'Có lỗi xảy ra, vui lòng thử lại'
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <LoginForm
        onSubmit={handleLoginSubmit}
        isLoading={isLoading}
        defaultValues={{
          email: '',
          rememberMe: false
        }}
      />
    </div>
  )
}

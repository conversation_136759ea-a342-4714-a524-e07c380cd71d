# Product Requirements Document (PRD)
## Shopee Coupon Finder & Product Comparison Platform

### 1. Tổng quan dự án

**Tên dự án:** Shopee Coupon Finder & Product Comparison Platform
**Mục tiêu:** Xây dựng nền tảng tìm kiếm mã giảm giá, so sánh sản phẩm và affiliate marketing tích hợp với AccessTrade API
**Đối tượng người dùng:** Người mua sắm online, deal hunter, affiliate marketer
**Tech Stack:** TanStack Start + Hono.dev + Cloudflare ecosystem + Full TanStack suite ⭐ CẬP NHẬT

### 2. <PERSON>ục tiêu kinh doanh

- **Chính:** Tạo doanh thu từ affiliate commission thông qua AccessTrade
- **Phụ:** Xây dựng cơ sở người dùng trung thành, tăng traffic website
- **KPI:** Click-through rate, conversion rate, commission revenue, user retention
- **Performance Target:** Global edge deployment với Cloudflare

### 3. T<PERSON>h năng chính

#### 3.1 Tìm kiếm mã giảm giá thông minh
- **Input:** URL sản phẩm Shopee hoặc từ khóa tìm kiếm
- **Output:** Danh sách mã giảm giá phù hợp, được sắp xếp theo độ ưu đãi
- **Logic:** 
  - Parse URL để xác định category/merchant
  - Gọi AccessTrade API `offers_informations` 
  - Filter theo merchant=shopee, status=1
  - Hiển thị với affiliate link

#### 3.2 So sánh sản phẩm
- **Tính năng:** So sánh tối đa 4 sản phẩm cùng lúc
- **Hiển thị:** Bảng so sánh với TanStack Table v8
- **Thông tin:** Tên, giá, discount, rating, sold, image, affiliate link
- **Data source:** AccessTrade Datafeeds API cached trong Cloudflare KV
- **UX:** Checkbox selection, drag & drop reorder
- **Performance:** Server-side filtering với Cloudflare Workers

#### 3.3 Sản phẩm bán chạy
- **Data source:** AccessTrade Top Selling Products API
- **Hiển thị:** Grid layout với pagination
- **Filter:** Theo category, price range, discount rate
- **CTA:** "Xem deal" button với affiliate link

#### 3.4 Mã giảm giá theo ngành hàng
- **Categories:** Fashion, Electronics, Beauty, Home & Living, Sports, Books, Food
- **Layout:** Category tabs với coupon cards
- **Data:** AccessTrade offers_informations API với filter categories
- **Features:** Search within category, sort by discount/expiry

#### 3.5 Trang khuyến mãi campaigns
- **Data source:** AccessTrade Campaigns API (approved campaigns)
- **Layout:** Campaign cards với banner images
- **Info:** Campaign name, merchant, commission rate, cookie duration
- **CTA:** "Tham gia ngay" với affiliate link

#### 3.6 Trang Admin
- **Authentication:** Supabase Auth
- **Features:**
  - Quản lý sản phẩm featured
  - Thống kê clicks/conversions
  - Cấu hình categories
  - Quản lý affiliate links
- **UI:** Dashboard với charts, tables

### 4. User Stories

#### 4.1 End User
- **US-001:** Là người dùng, tôi muốn dán URL sản phẩm Shopee để tìm mã giảm giá phù hợp
- **US-002:** Là người dùng, tôi muốn tìm kiếm sản phẩm bằng từ khóa và so sánh giá
- **US-003:** Là người dùng, tôi muốn xem sản phẩm bán chạy để tham khảo
- **US-004:** Là người dùng, tôi muốn xem mã giảm giá theo từng ngành hàng
- **US-005:** Là người dùng, tôi muốn xem các campaign khuyến mãi đang diễn ra

#### 4.2 Admin User
- **US-006:** Là admin, tôi muốn thêm sản phẩm featured để hiển thị ưu tiên
- **US-007:** Là admin, tôi muốn xem thống kê hiệu suất affiliate links
- **US-008:** Là admin, tôi muốn quản lý categories và tags

### 5. Yêu cầu kỹ thuật

#### 5.1 Tech Stack với Hono.dev Integration ⭐ CẬP NHẬT
- **Frontend Framework:** TanStack Start (Full-stack React framework)
- **API Framework:** Hono.dev (Ultrafast web framework cho Cloudflare Workers) ⭐ MỚI
- **Routing:** TanStack Router v1 (Frontend), Hono Router (API) ⭐ CẬP NHẬT
- **Data Fetching:** TanStack Query v5 với Hono RPC (Type-safe API calls) ⭐ CẬP NHẬT
- **Forms:** TanStack Form v0.29 (Type-safe forms)
- **Tables:** TanStack Table v8 (Headless table library)
- **State Management:** Zustand v4 (Client state)
- **Validation:** Zod v4 (Schema validation cho Hono routes) ⭐ CẬP NHẬT
- **UI:** Shadcn/ui, Tailwind CSS v4
- **Database:** Cloudflare D1 (SQLite)
- **ORM:** Drizzle ORM với type-safe queries
- **Cache:** Cloudflare KV Store + Hono Cache Middleware ⭐ CẬP NHẬT
- **Edge Functions:** Cloudflare Workers với Hono.dev ⭐ CẬP NHẬT
- **Middleware:** Hono middleware ecosystem (CORS, Auth, Rate Limiting) ⭐ MỚI
- **CDN:** Cloudflare CDN
- **Deployment:** Cloudflare Pages
- **API Integration:** AccessTrade API thông qua Hono routes ⭐ CẬP NHẬT

#### 5.2 Performance Requirements
- **Page Load:** < 1.5 seconds (Cloudflare Edge)
- **API Response:** < 500ms (Cloudflare Workers)
- **Mobile Responsive:** 100% compatibility
- **SEO Score:** > 95 (Lighthouse)
- **Global Latency:** < 100ms (Cloudflare CDN)

#### 5.3 Security Requirements
- **API Keys:** Cloudflare Environment variables
- **Authentication:** Cloudflare Access + JWT
- **Rate Limiting:** Cloudflare Workers rate limiting
- **Data Validation:** Zod v4 schemas cho tất cả inputs
- **DDoS Protection:** Cloudflare security features

### 6. Luồng người dùng chính với Hono.dev ⭐ CẬP NHẬT

#### 6.1 Tìm kiếm mã giảm giá với Hono RPC
1. User nhập URL/keyword (TanStack Form) → 2. TanStack Router navigation → 3. Hono RPC calls AccessTrade với type safety ⭐ CẬP NHẬT → 4. Hono cache middleware stores results trong KV ⭐ CẬP NHẬT → 5. TanStack Query displays results với Hono data ⭐ CẬP NHẬT → 6. User click "Copy code" với Hono analytics tracking ⭐ CẬP NHẬT

#### 6.2 So sánh sản phẩm với Hono API
1. User search keyword (TanStack Form) → 2. TanStack Query fetch data từ Hono RPC ⭐ CẬP NHẬT → 3. Display product grid với Hono validation ⭐ CẬP NHẬT → 4. User select products to compare → 5. TanStack Table shows comparison với Hono data ⭐ CẬP NHẬT → 6. TanStack Router navigate to affiliate link với Hono tracking ⭐ CẬP NHẬT

#### 6.3 Admin workflow với Hono Auth
1. Admin login (Hono JWT middleware + TanStack Start auth) ⭐ CẬP NHẬT → 2. TanStack Router protected routes với Hono auth check ⭐ CẬP NHẬT → 3. Manage products/categories (Drizzle ORM + Hono API) ⭐ CẬP NHẬT → 4. TanStack Query analytics từ Hono endpoints ⭐ CẬP NHẬT → 5. TanStack Table export reports với Hono streaming ⭐ CẬP NHẬT

### 7. Metrics & Analytics

#### 7.1 Business Metrics
- **Revenue:** Total affiliate commission
- **Traffic:** Daily/Monthly active users
- **Conversion:** Click-to-purchase rate
- **Retention:** User return rate

#### 7.2 Technical Metrics
- **Performance:** Core Web Vitals (Cloudflare Analytics)
- **Reliability:** Uptime 99.99% (Cloudflare SLA)
- **API Usage:** AccessTrade API calls/day
- **Error Rate:** < 0.1% (Cloudflare Workers)
- **Cache Hit Rate:** > 90% (Cloudflare KV)
- **Edge Response Time:** < 50ms

### 8. Roadmap với Hono.dev Integration ⭐ CẬP NHẬT

#### Phase 1 (MVP - 4 weeks) với Hono.dev
- Basic coupon search với TanStack Start + Hono RPC ⭐ CẬP NHẬT
- Product comparison với TanStack Table + Hono API ⭐ CẬP NHẬT
- Admin dashboard với TanStack Router protected routes + Hono auth ⭐ CẬP NHẬT
- AccessTrade integration với Hono routes + TanStack Query caching ⭐ CẬP NHẬT
- Hono middleware setup (CORS, Rate Limiting, Compression) ⭐ MỚI

#### Phase 2 (Enhancement - 2 weeks) với Hono Advanced Features
- Advanced filters với TanStack Form + Hono validation ⭐ CẬP NHẬT
- User favorites với TanStack Query mutations + Hono RPC ⭐ CẬP NHẬT
- PWA với TanStack Start + Hono streaming ⭐ CẬP NHẬT
- Real-time notifications với Hono WebSocket + TanStack Query ⭐ CẬP NHẬT
- Hono OpenAPI documentation generation ⭐ MỚI

#### Phase 3 (Scale - 2 weeks) với Hono Performance Optimization
- Multi-merchant support với TanStack Router + Hono routing ⭐ CẬP NHẬT
- AI recommendations với Cloudflare AI + Hono integration ⭐ CẬP NHẬT
- Social sharing với TanStack Router + Hono analytics ⭐ CẬP NHẬT
- Advanced analytics với TanStack Table + Hono streaming data ⭐ CẬP NHẬT
- Hono performance monitoring và optimization ⭐ MỚI

### 9. Risk & Mitigation

#### 9.1 Technical Risks
- **AccessTrade API limits:** Implement TanStack Query caching, Cloudflare Workers rate limiting
- **Performance issues:** TanStack Start optimization, Cloudflare CDN
- **Security vulnerabilities:** TanStack Start security features, Cloudflare protection
- **TanStack Start maturity:** Framework còn mới, có thể có breaking changes
- **D1 database limits:** Implement proper indexing với Drizzle ORM
- **Learning curve:** Team cần học TanStack ecosystem

#### 9.2 Business Risks
- **Commission changes:** Diversify affiliate partners
- **Competition:** Focus on unique value proposition
- **User acquisition:** SEO optimization, content marketing

### 10. Success Criteria

#### 10.1 Launch Criteria
- All core features functional với TanStack Start
- Mobile responsive design với PWA support
- Admin dashboard complete với TanStack Router protection
- AccessTrade integration tested với TanStack Query caching
- Performance targets met (< 1.5s load time)
- Type safety đảm bảo end-to-end

#### 10.2 Success Metrics (3 months)
- 10,000+ monthly active users
- $1,000+ monthly affiliate revenue
- 5%+ conversion rate
- 4.5+ user rating
- 99.99% uptime với Cloudflare
- < 100ms global response time
- Zero type errors trong production

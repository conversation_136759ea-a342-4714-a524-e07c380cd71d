import { Hono } from 'hono'
import { jwt } from 'hono/jwt'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { drizzle } from 'drizzle-orm/d1'
import { eq, sql } from 'drizzle-orm'
import { featuredProducts, categories } from '../../db/schema'
import type { Bindings, Variables } from '../hono-app'

// Validation schemas
const createFeaturedProductSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  title: z.string().min(1, 'Title is required'),
  imageUrl: z.string().url('Invalid image URL').optional(),
  affiliateLink: z.string().url('Invalid affiliate link'),
  categoryId: z.number().int().positive('Invalid category ID'),
  priority: z.number().int().min(0).default(0),
})

const updateFeaturedProductSchema = createFeaturedProductSchema.partial()

const createCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  slug: z.string().min(1, 'Category slug is required'),
  icon: z.string().optional(),
  description: z.string().optional(),
})

// Create admin routes
export const adminRoutes = new Hono<{
  Bindings: Bindings
  Variables: Variables
}>()

// JWT middleware for all admin routes
const authMiddleware = (c: any, next: any) => {
  const jwtMiddleware = jwt({
    secret: c.env.JWT_SECRET,
  })
  return jwtMiddleware(c, next)
}

// Admin middleware to check role
const adminMiddleware = async (c: any, next: any) => {
  const payload = c.get('jwtPayload')
  
  if (payload.role !== 'admin') {
    return c.json({
      error: 'Forbidden',
      message: 'Admin access required'
    }, 403)
  }
  
  await next()
}

// Apply middleware to all admin routes
adminRoutes.use('*', authMiddleware, adminMiddleware)

// Featured Products Management
adminRoutes.get('/featured-products', async (c) => {
  try {
    const db = drizzle(c.env.DB)
    
    const products = await db
      .select()
      .from(featuredProducts)
      .orderBy(featuredProducts.priority, featuredProducts.createdAt)
      .all()
    
    return c.json({
      success: true,
      message: 'Featured products retrieved successfully',
      data: { products }
    })
    
  } catch (error) {
    console.error('Get featured products error:', error)
    return c.json({
      error: 'Failed to get featured products',
      message: 'An error occurred while fetching featured products'
    }, 500)
  }
})

adminRoutes.post('/featured-products',
  zValidator('json', createFeaturedProductSchema),
  async (c) => {
    try {
      const data = c.req.valid('json')
      const db = drizzle(c.env.DB)
      
      const newProduct = await db
        .insert(featuredProducts)
        .values(data)
        .returning()
        .get()
      
      return c.json({
        success: true,
        message: 'Featured product created successfully',
        data: { product: newProduct }
      }, 201)
      
    } catch (error) {
      console.error('Create featured product error:', error)
      return c.json({
        error: 'Failed to create featured product',
        message: 'An error occurred while creating featured product'
      }, 500)
    }
  }
)

adminRoutes.put('/featured-products/:id',
  zValidator('json', updateFeaturedProductSchema),
  async (c) => {
    try {
      const id = parseInt(c.req.param('id'))
      const data = c.req.valid('json')
      const db = drizzle(c.env.DB)
      
      const updatedProduct = await db
        .update(featuredProducts)
        .set(data)
        .where(eq(featuredProducts.id, id))
        .returning()
        .get()
      
      if (!updatedProduct) {
        return c.json({
          error: 'Featured product not found',
          message: 'The requested featured product was not found'
        }, 404)
      }
      
      return c.json({
        success: true,
        message: 'Featured product updated successfully',
        data: { product: updatedProduct }
      })
      
    } catch (error) {
      console.error('Update featured product error:', error)
      return c.json({
        error: 'Failed to update featured product',
        message: 'An error occurred while updating featured product'
      }, 500)
    }
  }
)

adminRoutes.delete('/featured-products/:id', async (c) => {
  try {
    const id = parseInt(c.req.param('id'))
    const db = drizzle(c.env.DB)
    
    const deletedProduct = await db
      .delete(featuredProducts)
      .where(eq(featuredProducts.id, id))
      .returning()
      .get()
    
    if (!deletedProduct) {
      return c.json({
        error: 'Featured product not found',
        message: 'The requested featured product was not found'
      }, 404)
    }
    
    return c.json({
      success: true,
      message: 'Featured product deleted successfully'
    })
    
  } catch (error) {
    console.error('Delete featured product error:', error)
    return c.json({
      error: 'Failed to delete featured product',
      message: 'An error occurred while deleting featured product'
    }, 500)
  }
})

// Categories Management
adminRoutes.get('/categories', async (c) => {
  try {
    const db = drizzle(c.env.DB)
    
    const allCategories = await db
      .select()
      .from(categories)
      .orderBy(categories.name)
      .all()
    
    return c.json({
      success: true,
      message: 'Categories retrieved successfully',
      data: { categories: allCategories }
    })
    
  } catch (error) {
    console.error('Get categories error:', error)
    return c.json({
      error: 'Failed to get categories',
      message: 'An error occurred while fetching categories'
    }, 500)
  }
})

adminRoutes.post('/categories',
  zValidator('json', createCategorySchema),
  async (c) => {
    try {
      const data = c.req.valid('json')
      const db = drizzle(c.env.DB)
      
      const newCategory = await db
        .insert(categories)
        .values(data)
        .returning()
        .get()
      
      return c.json({
        success: true,
        message: 'Category created successfully',
        data: { category: newCategory }
      }, 201)
      
    } catch (error) {
      console.error('Create category error:', error)
      return c.json({
        error: 'Failed to create category',
        message: 'An error occurred while creating category'
      }, 500)
    }
  }
)

// System Status
adminRoutes.get('/status', async (c) => {
  try {
    const db = drizzle(c.env.DB)
    
    // Get basic stats
    const featuredProductsCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(featuredProducts)
      .get()

    const categoriesCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(categories)
      .get()
    
    // Get KV storage info
    const cacheKeys = await c.env.CACHE.list({ limit: 1 })
    const analyticsKeys = await c.env.ANALYTICS.list({ limit: 1 })
    
    return c.json({
      success: true,
      message: 'System status retrieved successfully',
      data: {
        database: {
          featuredProducts: featuredProductsCount?.count || 0,
          categories: categoriesCount?.count || 0,
        },
        cache: {
          hasData: cacheKeys.keys.length > 0,
          keyCount: cacheKeys.keys.length,
        },
        analytics: {
          hasData: analyticsKeys.keys.length > 0,
          keyCount: analyticsKeys.keys.length,
        },
        environment: {
          nodeEnv: c.env.NODE_ENV,
          appVersion: c.env.APP_VERSION,
          cachingEnabled: c.env.ENABLE_CACHING === 'true',
          analyticsEnabled: c.env.ENABLE_ANALYTICS === 'true',
          debugEnabled: c.env.ENABLE_DEBUG === 'true',
        }
      }
    })
    
  } catch (error) {
    console.error('Get system status error:', error)
    return c.json({
      error: 'Failed to get system status',
      message: 'An error occurred while fetching system status'
    }, 500)
  }
})

export default adminRoutes

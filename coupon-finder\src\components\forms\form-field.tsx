/**
 * TanStack Form Field Component với Zod v4 Integration
 * 
 * Reusable form field component với type-safe validation
 * và integration với shadcn/ui components
 */

import React from 'react';
import { type FieldApi, type AnyFieldApi } from '@tanstack/react-form';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';

// ===== TYPE DEFINITIONS =====

export interface FormFieldProps {
  field: AnyFieldApi;
  label?: string;
  placeholder?: string;
  description?: string;
  required?: boolean;
  className?: string;
  children: React.ReactNode;
}

export interface FormFieldErrorProps {
  field: AnyFieldApi;
  className?: string;
}

export interface FormFieldLabelProps {
  field: AnyFieldApi;
  label: string;
  required?: boolean;
  className?: string;
}

export interface FormFieldDescriptionProps {
  description: string;
  className?: string;
}

// ===== FORM FIELD COMPONENTS =====

/**
 * Main Form Field Wrapper Component
 */
export function FormField({
  field,
  label,
  placeholder,
  description,
  required = false,
  className,
  children
}: FormFieldProps) {
  const hasError = !field.state.meta.isValid && field.state.meta.isTouched;

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <FormFieldLabel
          field={field}
          label={label}
          required={required}
        />
      )}
      
      <div className="relative">
        {children}
      </div>
      
      {description && !hasError && (
        <FormFieldDescription description={description} />
      )}
      
      <FormFieldError field={field} />
    </div>
  );
}

/**
 * Form Field Label Component
 */
export function FormFieldLabel({
  field,
  label,
  required = false,
  className
}: FormFieldLabelProps) {
  return (
    <Label
      htmlFor={field.name}
      className={cn(
        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
        className
      )}
    >
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
  );
}

/**
 * Form Field Error Component
 */
export function FormFieldError({
  field,
  className
}: FormFieldErrorProps) {
  const hasError = !field.state.meta.isValid && field.state.meta.isTouched;
  const errorMessage = field.state.meta.errors[0];

  if (!hasError || !errorMessage) {
    return null;
  }

  return (
    <p className={cn(
      "text-sm text-red-600 dark:text-red-400",
      className
    )}>
      {errorMessage}
    </p>
  );
}

/**
 * Form Field Description Component
 */
export function FormFieldDescription({
  description,
  className
}: FormFieldDescriptionProps) {
  return (
    <p className={cn(
      "text-sm text-gray-600 dark:text-gray-400",
      className
    )}>
      {description}
    </p>
  );
}

// ===== FORM FIELD INPUT COMPONENTS =====

/**
 * Text Input Field Component
 */
export interface FormTextInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange'> {
  field: AnyFieldApi;
  label?: string;
  description?: string;
  required?: boolean;
}

export function FormTextInput({
  field,
  label,
  description,
  required = false,
  className,
  ...props
}: FormTextInputProps) {
  const hasError = !field.state.meta.isValid && field.state.meta.isTouched;

  return (
    <FormField
      field={field}
      label={label}
      description={description}
      required={required}
    >
      <input
        id={field.name}
        name={field.name}
        value={field.state.value || ''}
        onChange={(e) => field.handleChange(e.target.value)}
        onBlur={field.handleBlur}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          hasError && "border-red-500 focus-visible:ring-red-500",
          className
        )}
        {...props}
      />
    </FormField>
  );
}

/**
 * Textarea Field Component
 */
export interface FormTextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'value' | 'onChange'> {
  field: AnyFieldApi;
  label?: string;
  description?: string;
  required?: boolean;
}

export function FormTextarea({
  field,
  label,
  description,
  required = false,
  className,
  ...props
}: FormTextareaProps) {
  const hasError = !field.state.meta.isValid && field.state.meta.isTouched;

  return (
    <FormField
      field={field}
      label={label}
      description={description}
      required={required}
    >
      <textarea
        id={field.name}
        name={field.name}
        value={field.state.value || ''}
        onChange={(e) => field.handleChange(e.target.value)}
        onBlur={field.handleBlur}
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          hasError && "border-red-500 focus-visible:ring-red-500",
          className
        )}
        {...props}
      />
    </FormField>
  );
}

/**
 * Select Field Component
 */
export interface FormSelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'value' | 'onChange'> {
  field: AnyFieldApi;
  label?: string;
  description?: string;
  required?: boolean;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  placeholder?: string;
}

export function FormSelect({
  field,
  label,
  description,
  required = false,
  options,
  placeholder = "Chọn một tùy chọn",
  className,
  ...props
}: FormSelectProps) {
  const hasError = !field.state.meta.isValid && field.state.meta.isTouched;

  return (
    <FormField
      field={field}
      label={label}
      description={description}
      required={required}
    >
      <select
        id={field.name}
        name={field.name}
        value={field.state.value || ''}
        onChange={(e) => field.handleChange(e.target.value)}
        onBlur={field.handleBlur}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          hasError && "border-red-500 focus-visible:ring-red-500",
          className
        )}
        {...props}
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
    </FormField>
  );
}

/**
 * Checkbox Field Component
 */
export interface FormCheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'checked' | 'onChange'> {
  field: AnyFieldApi;
  label?: string;
  description?: string;
  required?: boolean;
}

export function FormCheckbox({
  field,
  label,
  description,
  required = false,
  className,
  ...props
}: FormCheckboxProps) {
  const hasError = !field.state.meta.isValid && field.state.meta.isTouched;

  return (
    <FormField
      field={field}
      description={description}
      required={required}
      className="flex flex-row items-start space-x-3 space-y-0"
    >
      <input
        type="checkbox"
        id={field.name}
        name={field.name}
        checked={field.state.value || false}
        onChange={(e) => field.handleChange(e.target.checked)}
        onBlur={field.handleBlur}
        className={cn(
          "h-4 w-4 rounded border border-input text-primary focus:ring-2 focus:ring-ring focus:ring-offset-2",
          hasError && "border-red-500 focus:ring-red-500",
          className
        )}
        {...props}
      />
      {label && (
        <div className="grid gap-1.5 leading-none">
          <Label
            htmlFor={field.name}
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        </div>
      )}
    </FormField>
  );
}

// ===== FORM VALIDATION INDICATORS =====

/**
 * Form Validation Status Component
 */
export interface FormValidationStatusProps {
  field: AnyFieldApi;
  showValidIcon?: boolean;
  className?: string;
}

export function FormValidationStatus({
  field,
  showValidIcon = true,
  className
}: FormValidationStatusProps) {
  const isValid = field.state.meta.isValid;
  const isTouched = field.state.meta.isTouched;
  const isValidating = field.state.meta.isValidating;

  if (!isTouched) return null;

  if (isValidating) {
    return (
      <div className={cn("absolute right-3 top-1/2 -translate-y-1/2", className)}>
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600" />
      </div>
    );
  }

  if (isValid && showValidIcon) {
    return (
      <div className={cn("absolute right-3 top-1/2 -translate-y-1/2", className)}>
        <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      </div>
    );
  }

  if (!isValid) {
    return (
      <div className={cn("absolute right-3 top-1/2 -translate-y-1/2", className)}>
        <svg className="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>
    );
  }

  return null;
}

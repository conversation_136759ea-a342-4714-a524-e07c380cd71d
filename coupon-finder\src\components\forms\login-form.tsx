/**
 * Login Form Component
 * 
 * Sử dụng TanStack Form với Zod v4 validation
 * Demo authentication form với type-safe validation
 */

import React, { useState } from 'react';
import { useForm } from '@tanstack/react-form';
import { Eye, EyeOff, LogIn, Mail, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  FormTextInput, 
  FormCheckbox, 
  FormField 
} from './form-field';
import { 
  loginSchema, 
  type LoginFormData 
} from '@/lib/validation-schemas';
import { 
  useZodForm, 
  createZodValidator, 
  fieldValidators,
  type FormSubmissionResult 
} from '@/lib/form-utils';

// ===== TYPE DEFINITIONS =====

export interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<FormSubmissionResult<LoginFormData>>;
  onForgotPassword?: () => void;
  onRegister?: () => void;
  defaultValues?: Partial<LoginFormData>;
  isLoading?: boolean;
  className?: string;
}

// ===== MAIN COMPONENT =====

export function LoginForm({
  onSubmit,
  onForgotPassword,
  onRegister,
  defaultValues = {},
  isLoading = false,
  className
}: LoginFormProps) {
  const [showPassword, setShowPassword] = useState(false);

  // Sử dụng custom useZodForm hook với type-safe validation
  const form = useZodForm({
    schema: loginSchema,
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
      ...defaultValues
    } as LoginFormData,
    onSubmit,
    onSubmitInvalid: (errors) => {
      console.error('Login form validation errors:', errors);
    }
  });

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          Đăng nhập
        </CardTitle>
        <CardDescription className="text-center">
          Nhập email và mật khẩu để truy cập tài khoản
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-4"
        >
          {/* Email Field */}
          <form.Field
            name="email"
            validators={{
              onChange: createZodValidator(loginSchema.shape.email),
              onBlur: fieldValidators.email('Email không hợp lệ')
            }}
          >
            {(field) => (
              <FormField
                field={field}
                label="Email"
                required
              >
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id={field.name}
                    name={field.name}
                    type="email"
                    value={field.state.value || ''}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    className={cn(
                      "flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                      !field.state.meta.isValid && field.state.meta.isTouched && "border-red-500 focus-visible:ring-red-500"
                    )}
                  />
                </div>
              </FormField>
            )}
          </form.Field>

          {/* Password Field */}
          <form.Field
            name="password"
            validators={{
              onChange: createZodValidator(loginSchema.shape.password),
              onBlur: fieldValidators.minLength(6, 'Mật khẩu phải có ít nhất 6 ký tự')
            }}
          >
            {(field) => (
              <FormField
                field={field}
                label="Mật khẩu"
                required
              >
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2">
                    <Lock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id={field.name}
                    name={field.name}
                    type={showPassword ? "text" : "password"}
                    value={field.state.value || ''}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder="Nhập mật khẩu"
                    autoComplete="current-password"
                    className={cn(
                      "flex h-10 w-full rounded-md border border-input bg-background pl-10 pr-10 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                      !field.state.meta.isValid && field.state.meta.isTouched && "border-red-500 focus-visible:ring-red-500"
                    )}
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </FormField>
            )}
          </form.Field>

          {/* Remember Me & Forgot Password */}
          <div className="flex items-center justify-between">
            <form.Field
              name="rememberMe"
              validators={{
                onChange: createZodValidator(loginSchema.shape.rememberMe)
              }}
            >
              {(field) => (
                <FormCheckbox
                  field={field}
                  label="Ghi nhớ đăng nhập"
                />
              )}
            </form.Field>

            {onForgotPassword && (
              <button
                type="button"
                onClick={onForgotPassword}
                className="text-sm text-primary hover:underline"
              >
                Quên mật khẩu?
              </button>
            )}
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={!form.state.isValid || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Đang đăng nhập...
              </>
            ) : (
              <>
                <LogIn className="mr-2 h-4 w-4" />
                Đăng nhập
              </>
            )}
          </Button>

          {/* Register Link */}
          {onRegister && (
            <div className="text-center">
              <span className="text-sm text-gray-600">
                Chưa có tài khoản?{' '}
                <button
                  type="button"
                  onClick={onRegister}
                  className="text-primary hover:underline font-medium"
                >
                  Đăng ký ngay
                </button>
              </span>
            </div>
          )}

          {/* Form Debug Info (Development only) */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm text-gray-500">
                Debug Info (Development)
              </summary>
              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify({
                  values: form.state.values,
                  errors: form.state.errors,
                  isValid: form.state.isValid,
                  isSubmitting: form.state.isSubmitting,
                  errorMap: form.state.errorMap
                }, null, 2)}
              </pre>
            </details>
          )}
        </form>
      </CardContent>
    </Card>
  );
}

// ===== USAGE EXAMPLE =====

/**
 * Example usage của LoginForm
 */
export function LoginFormExample() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: LoginFormData): Promise<FormSubmissionResult<LoginFormData>> => {
    setIsLoading(true);
    
    try {
      console.log('Login attempt with data:', data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock validation
      if (data.email === '<EMAIL>' && data.password === 'password123') {
        return {
          success: true,
          data,
          message: 'Đăng nhập thành công!'
        };
      } else {
        return {
          success: false,
          errors: {
            email: 'Email hoặc mật khẩu không đúng'
          },
          message: 'Đăng nhập thất bại'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Có lỗi xảy ra khi đăng nhập'
      };
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    console.log('Forgot password clicked');
    // Navigate to forgot password page
  };

  const handleRegister = () => {
    console.log('Register clicked');
    // Navigate to register page
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <LoginForm
        onSubmit={handleSubmit}
        onForgotPassword={handleForgotPassword}
        onRegister={handleRegister}
        isLoading={isLoading}
        defaultValues={{
          email: '<EMAIL>',
          rememberMe: true
        }}
      />
    </div>
  );
}

// ===== ASYNC VALIDATION EXAMPLE =====

/**
 * Example với async validation cho email uniqueness
 */
export function LoginFormWithAsyncValidation() {
  const form = useZodForm({
    schema: loginSchema,
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false
    } as LoginFormData,
    onSubmit: async (data) => {
      console.log('Submitting:', data);
      return { success: true, data };
    }
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        form.handleSubmit();
      }}
    >
      {/* Email với async validation */}
      <form.Field
        name="email"
        validators={{
          onChange: createZodValidator(loginSchema.shape.email),
          onChangeAsync: async ({ value }) => {
            if (!value) return undefined;
            
            // Simulate API call để check email existence
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Mock check
            if (value === '<EMAIL>') {
              return 'Email này đã được sử dụng';
            }
            
            return undefined;
          }
        }}
        asyncDebounceMs={300}
      >
        {(field) => (
          <FormTextInput
            field={field}
            label="Email"
            placeholder="<EMAIL>"
            required
          />
        )}
      </form.Field>

      {/* Rest of form... */}
    </form>
  );
}

import * as React from 'react'
import { useQuery } from '@tanstack/react-query'
import { ChevronLeft, ChevronRight, Filter, Grid, List, RefreshCw } from 'lucide-react'
import { CouponCard } from './coupon-card'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { api } from '@/api/hono-client'
import { cn } from '@/lib/utils'

interface CouponListProps {
  category?: string
  merchant?: string
  keyword?: string
  sortBy?: 'discount' | 'expiry' | 'popularity' | 'newest'
  viewMode?: 'grid' | 'list'
  showFilters?: boolean
  showPagination?: boolean
  pageSize?: number
  className?: string
}

interface CouponFilters {
  category: string
  merchant: string
  sortBy: 'discount' | 'expiry' | 'popularity' | 'newest'
}

export function CouponList({
  category = '',
  merchant = '',
  keyword = '',
  sortBy = 'discount',
  viewMode: initialViewMode = 'grid',
  showFilters = true,
  showPagination = true,
  pageSize = 12,
  className,
}: CouponListProps) {
  const [currentPage, setCurrentPage] = React.useState(1)
  const [viewMode, setViewMode] = React.useState(initialViewMode)
  const [filters, setFilters] = React.useState<CouponFilters>({
    category,
    merchant,
    sortBy,
  })

  // Fetch coupons với TanStack Query và Hono RPC
  const {
    data: couponsData,
    isLoading,
    error,
    refetch,
    isFetching,
  } = useQuery({
    queryKey: ['coupons', 'list', keyword, filters, currentPage, pageSize],
    queryFn: async () => {
      const params = {
        keyword: keyword || undefined,
        category: filters.category || undefined,
        merchant: filters.merchant || undefined,
        sortBy: filters.sortBy,
        limit: pageSize,
        offset: (currentPage - 1) * pageSize,
      }

      const result = await api.coupons.search({
        ...params,
        sortBy: filters.sortBy as 'discount' | 'expiry' | 'popularity' | 'newest'
      })
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch coupons')
      }

      return result.data
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
  })

  const coupons = couponsData?.coupons || []
  const totalCoupons = couponsData?.total || 0
  const totalPages = Math.ceil(totalCoupons / pageSize)

  const handleFilterChange = (key: keyof CouponFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1) // Reset to first page when filters change
  }

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }

  const handleCouponClick = (couponId: string) => {
    console.log('Coupon clicked in list:', couponId)
    // Additional analytics or tracking logic
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header với filters và view controls */}
      {showFilters && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Bộ lọc và hiển thị
              </CardTitle>
              
              {/* View Mode Toggle */}
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Category Filter */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Danh mục
                </label>
                <Select 
                  value={filters.category} 
                  onValueChange={(value) => handleFilterChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tất cả danh mục" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tất cả danh mục</SelectItem>
                    <SelectItem value="fashion">Thời trang</SelectItem>
                    <SelectItem value="electronics">Điện tử</SelectItem>
                    <SelectItem value="beauty">Làm đẹp</SelectItem>
                    <SelectItem value="home">Nhà cửa</SelectItem>
                    <SelectItem value="sports">Thể thao</SelectItem>
                    <SelectItem value="books">Sách</SelectItem>
                    <SelectItem value="food">Thực phẩm</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Merchant Filter */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Cửa hàng
                </label>
                <Select 
                  value={filters.merchant} 
                  onValueChange={(value) => handleFilterChange('merchant', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Tất cả cửa hàng" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Tất cả cửa hàng</SelectItem>
                    <SelectItem value="shopee">Shopee</SelectItem>
                    <SelectItem value="lazada">Lazada</SelectItem>
                    <SelectItem value="tiki">Tiki</SelectItem>
                    <SelectItem value="sendo">Sendo</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort Filter */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Sắp xếp theo
                </label>
                <Select 
                  value={filters.sortBy} 
                  onValueChange={(value) => handleFilterChange('sortBy', value as CouponFilters['sortBy'])}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="discount">Giảm giá cao nhất</SelectItem>
                    <SelectItem value="expiry">Sắp hết hạn</SelectItem>
                    <SelectItem value="popularity">Phổ biến nhất</SelectItem>
                    <SelectItem value="newest">Mới nhất</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold">
            {isLoading ? 'Đang tải...' : `${totalCoupons} mã giảm giá`}
          </h2>
          {keyword && (
            <Badge variant="outline">
              Từ khóa: {keyword}
            </Badge>
          )}
          {error && (
            <Badge variant="destructive">
              Có lỗi xảy ra
            </Badge>
          )}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          disabled={isFetching}
        >
          <RefreshCw className={cn("h-4 w-4 mr-2", isFetching && "animate-spin")} />
          Làm mới
        </Button>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        }`}>
          {[...Array(pageSize)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-3 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Error State */}
      {error && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-red-500 text-lg mb-2">⚠️ Có lỗi xảy ra</div>
            <p className="text-gray-600 mb-4">
              {error instanceof Error ? error.message : 'Không thể tải danh sách mã giảm giá'}
            </p>
            <Button onClick={() => refetch()}>
              Thử lại
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Coupons Grid */}
      {!isLoading && !error && coupons.length > 0 && (
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        }`}>
          {coupons.map((coupon: any, index: number) => (
            <CouponCard
              key={coupon.id || index}
              id={coupon.id}
              title={coupon.title || coupon.name}
              description={coupon.description || coupon.content}
              discount={coupon.discount}
              code={coupon.code}
              expiryDate={coupon.expiryDate || coupon.end_time}
              store={coupon.store || coupon.merchant}
              category={coupon.category}
              affiliateLink={coupon.affiliateLink || coupon.aff_link}
              imageUrl={coupon.imageUrl || coupon.image}
              isExpired={coupon.isExpired || (coupon.end_time && new Date(coupon.end_time) < new Date())}
              className={viewMode === 'list' ? 'flex-row' : ''}
              onCouponClick={handleCouponClick}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && coupons.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">🎫</div>
            <h3 className="text-xl font-semibold mb-2">
              Không tìm thấy mã giảm giá
            </h3>
            <p className="text-gray-600 mb-4">
              Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
            </p>
            <Button variant="outline" onClick={() => {
              setFilters({ category: '', merchant: '', sortBy: 'discount' })
              setCurrentPage(1)
            }}>
              Xóa bộ lọc
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {showPagination && !isLoading && !error && totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage <= 1}
          >
            <ChevronLeft className="h-4 w-4" />
            Trước
          </Button>
          
          <div className="flex items-center gap-1">
            {[...Array(Math.min(5, totalPages))].map((_, i) => {
              const page = i + 1
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </Button>
              )
            })}
            {totalPages > 5 && (
              <>
                <span className="px-2">...</span>
                <Button
                  variant={currentPage === totalPages ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
          >
            Sau
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}

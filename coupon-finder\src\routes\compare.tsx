import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { ProductComparisonTable } from '@/components/tables/product-comparison-table';
import {
  Search,
  Plus,
  X,
  Star,
  ExternalLink,
} from 'lucide-react';

export const Route = createFileRoute('/compare')({
  component: ComparePage,
});

function ComparePage() {
  const [selectedProducts, setSelectedProducts] = useState(mockSelectedProducts);
  const [searchQuery, setSearchQuery] = useState('');





  const handleAddToFavorites = (productId: string) => {
    console.log('Add to favorites:', productId);
    // Implement favorites logic
  };

  const handleShare = (productId: string) => {
    console.log('Share product:', productId);
    // Implement share logic
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header Section */}
      <div className='text-center mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 mb-4'>
          ⚖️ So Sánh Sản Phẩm
        </h1>
        <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
          Tìm kiếm và so sánh các sản phẩm để đưa ra quyết định mua sắm thông
          minh nhất
        </p>
      </div>

      {/* Search Section */}
      <div className='max-w-2xl mx-auto mb-8'>
        <div className='relative'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5' />
          <Input
            placeholder='Tìm kiếm sản phẩm để so sánh...'
            className='pl-10 pr-4 py-3 text-lg'
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Button className='absolute right-2 top-1/2 transform -translate-y-1/2'>
            Tìm kiếm
          </Button>
        </div>
      </div>

      {/* Comparison Table Section */}
      <div className='mb-8'>
        <ProductComparisonTable
          products={selectedProducts}
          onAddToFavorites={handleAddToFavorites}
          onShare={handleShare}
        />
      </div>

      {/* Product Search Results */}
      <div>
        <h2 className='text-2xl font-semibold text-gray-900 mb-4'>
          Kết Quả Tìm Kiếm
        </h2>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
          {mockProducts.map(product => (
            <ProductCard
              key={product.id}
              product={product}
              selectedProducts={selectedProducts}
              onToggleSelection={(product) => {
                const isSelected = selectedProducts.some(p => p.id === product.id);
                if (isSelected) {
                  setSelectedProducts(prev => prev.filter(p => p.id !== product.id.toString()));
                } else {
                  if (selectedProducts.length < 4) {
                    setSelectedProducts(prev => [...prev, {
                      ...product,
                      id: product.id.toString(),
                      affiliateLink: 'https://example.com/product',
                      features: ['Feature 1', 'Feature 2'],
                      sold: 1000,
                      category: 'Điện thoại',
                    }]);
                  }
                }
              }}
            />
          ))}
        </div>
      </div>

      {/* Load More Button */}
      <div className='text-center mt-8'>
        <Button variant='outline' size='lg'>
          Xem thêm sản phẩm
        </Button>
      </div>
    </div>
  );
}

function ProductCard({
  product,
  selectedProducts,
  onToggleSelection
}: {
  product: any;
  selectedProducts: any[];
  onToggleSelection: (product: any) => void;
}) {
  const isSelected = selectedProducts.some(p => p.id === product.id);

  return (
    <Card className='hover:shadow-lg transition-shadow duration-200'>
      <CardHeader className='pb-3'>
        <div className='relative'>
          <img
            src={product.image}
            alt={product.name}
            className='w-full h-48 object-cover rounded-lg mb-3'
          />
          <div className='absolute top-2 left-2'>
            <Checkbox checked={isSelected} className='bg-white shadow-sm' />
          </div>
          {product.discount && (
            <Badge variant='destructive' className='absolute top-2 right-2'>
              -{product.discount}%
            </Badge>
          )}
        </div>
        <CardTitle className='text-lg font-semibold text-gray-900 line-clamp-2'>
          {product.name}
        </CardTitle>
        <CardDescription className='text-sm text-gray-600'>
          {product.store}
        </CardDescription>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Price */}
          <div className='flex items-center gap-2'>
            <span className='text-xl font-bold text-red-600'>
              {product.price.toLocaleString('vi-VN')}đ
            </span>
            {product.originalPrice && (
              <span className='text-sm text-gray-500 line-through'>
                {product.originalPrice.toLocaleString('vi-VN')}đ
              </span>
            )}
          </div>

          {/* Rating */}
          <div className='flex items-center gap-2'>
            <div className='flex items-center'>
              <Star className='h-4 w-4 fill-yellow-400 text-yellow-400' />
              <span className='text-sm font-medium ml-1'>{product.rating}</span>
            </div>
            <span className='text-sm text-gray-500'>
              ({product.reviews} đánh giá)
            </span>
          </div>

          {/* Action Buttons */}
          <div className='flex gap-2 pt-2'>
            <Button
              variant={isSelected ? 'secondary' : 'default'}
              className='flex-1'
              size='sm'
              onClick={() => onToggleSelection(product)}
            >
              {isSelected ? (
                <>
                  <X className='h-4 w-4 mr-2' />
                  Bỏ chọn
                </>
              ) : (
                <>
                  <Plus className='h-4 w-4 mr-2' />
                  So sánh
                </>
              )}
            </Button>
            <Button variant='outline' size='sm' className='px-3'>
              <ExternalLink className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}



// Mock data for development
const mockSelectedProducts = [
  {
    id: '1',
    name: 'iPhone 15 Pro Max 256GB',
    store: 'Apple Store',
    price: 29990000,
    originalPrice: 32990000,
    discount: 9,
    rating: 4.8,
    reviews: 1250,
    image: '/api/placeholder/300/300',
    affiliateLink: 'https://example.com/iphone',
    features: ['A17 Pro chip', '5x Zoom', 'Titanium'],
    sold: 15000,
    category: 'Điện thoại',
  },
  {
    id: '2',
    name: 'Samsung Galaxy S24 Ultra 256GB',
    store: 'Samsung Store',
    price: 27990000,
    originalPrice: 30990000,
    discount: 10,
    rating: 4.7,
    reviews: 980,
    image: '/api/placeholder/300/300',
    affiliateLink: 'https://example.com/samsung',
    features: ['S Pen', '200MP Camera', 'AI Features'],
    sold: 12000,
    category: 'Điện thoại',
  },
];

const mockProducts = [
  {
    id: 1,
    name: 'iPhone 15 Pro Max 256GB',
    store: 'Apple Store',
    price: 29990000,
    originalPrice: 32990000,
    discount: 9,
    rating: 4.8,
    reviews: 1250,
    image: '/api/placeholder/300/300',
  },
  {
    id: 2,
    name: 'Samsung Galaxy S24 Ultra 256GB',
    store: 'Samsung Store',
    price: 27990000,
    originalPrice: 30990000,
    discount: 10,
    rating: 4.7,
    reviews: 980,
    image: '/api/placeholder/300/300',
  },
  {
    id: 3,
    name: 'Xiaomi 14 Ultra 512GB',
    store: 'Xiaomi Store',
    price: 24990000,
    originalPrice: 26990000,
    discount: 7,
    rating: 4.6,
    reviews: 750,
    image: '/api/placeholder/300/300',
  },
  {
    id: 4,
    name: 'OPPO Find X7 Ultra 256GB',
    store: 'OPPO Store',
    price: 22990000,
    originalPrice: 24990000,
    discount: 8,
    rating: 4.5,
    reviews: 620,
    image: '/api/placeholder/300/300',
  },
];

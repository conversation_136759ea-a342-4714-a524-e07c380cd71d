import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-lg border-0 px-2.5 py-1 text-xs font-semibold w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-all duration-300 overflow-hidden shadow-soft",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-primary text-white [a&]:hover:shadow-medium",
        secondary:
          "bg-gradient-secondary text-gray-800 [a&]:hover:shadow-medium",
        destructive:
          "bg-destructive text-white [a&]:hover:bg-destructive/90 [a&]:hover:shadow-medium focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "text-foreground border border-border bg-background [a&]:hover:bg-accent [a&]:hover:text-accent-foreground [a&]:hover:shadow-medium",
        success:
          "bg-gradient-to-r from-green-500 to-emerald-500 text-white [a&]:hover:shadow-medium",
        warning:
          "bg-gradient-to-r from-orange-500 to-yellow-500 text-white [a&]:hover:shadow-medium",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span"

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  )
}

export { Badge, badgeVariants }

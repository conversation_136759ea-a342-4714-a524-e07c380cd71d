/**
 * Admin ProductManagement Table với TanStack Table v8
 * 
 * Features:
 * - Advanced table với sorting, filtering, pagination
 * - Bulk operations (select all, delete, export)
 * - Inline editing
 * - Status management
 * - Responsive design
 */

import React, { useState } from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  RowSelectionState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Download,
  Upload,
  Plus,
  Filter,
  Search,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from 'lucide-react';

// Types
interface AdminProduct {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  category: string;
  store: string;
  status: 'active' | 'inactive' | 'pending' | 'rejected';
  featured: boolean;
  rating: number;
  reviews: number;
  sold: number;
  image: string;
  affiliateLink: string;
  createdAt: string;
  updatedAt: string;
}

interface AdminProductManagementTableProps {
  products: AdminProduct[];
  onEdit?: (product: AdminProduct) => void;
  onDelete?: (productId: string) => void;
  onBulkDelete?: (productIds: string[]) => void;
  onStatusChange?: (productId: string, status: AdminProduct['status']) => void;
  onFeaturedToggle?: (productId: string, featured: boolean) => void;
  onExport?: (productIds?: string[]) => void;
  onImport?: () => void;
  onAdd?: () => void;
  className?: string;
}

export function AdminProductManagementTable({
  products,
  onEdit,
  onDelete,
  onBulkDelete,
  onStatusChange,
  onFeaturedToggle,
  onExport,
  onImport,
  onAdd,
  className,
}: AdminProductManagementTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = useState('');

  // Status badge variants
  const getStatusBadge = (status: AdminProduct['status']) => {
    const variants = {
      active: { variant: 'default' as const, label: 'Hoạt động' },
      inactive: { variant: 'secondary' as const, label: 'Không hoạt động' },
      pending: { variant: 'outline' as const, label: 'Chờ duyệt' },
      rejected: { variant: 'destructive' as const, label: 'Từ chối' },
    };
    return variants[status];
  };

  // Define columns
  const columns: ColumnDef<AdminProduct>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Chọn tất cả"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Chọn hàng"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: 'image',
      header: 'Hình ảnh',
      cell: ({ row }) => (
        <img
          src={row.getValue('image')}
          alt={row.getValue('name')}
          className="w-12 h-12 object-cover rounded-lg"
        />
      ),
      enableSorting: false,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium"
        >
          Tên sản phẩm
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="max-w-[200px]">
          <div className="font-medium line-clamp-2">{row.getValue('name')}</div>
          <div className="text-sm text-muted-foreground">{row.original.store}</div>
        </div>
      ),
    },
    {
      accessorKey: 'category',
      header: 'Danh mục',
      cell: ({ row }) => (
        <Badge variant="outline">{row.getValue('category')}</Badge>
      ),
    },
    {
      accessorKey: 'price',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium"
        >
          Giá
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const price = row.getValue('price') as number;
        const originalPrice = row.original.originalPrice;
        const discount = row.original.discount;
        
        return (
          <div className="text-right">
            <div className="font-medium text-red-600">
              {price.toLocaleString('vi-VN')}đ
            </div>
            {originalPrice && (
              <div className="text-sm text-muted-foreground line-through">
                {originalPrice.toLocaleString('vi-VN')}đ
              </div>
            )}
            {discount && (
              <Badge variant="destructive" className="text-xs">
                -{discount}%
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Trạng thái',
      cell: ({ row }) => {
        const status = row.getValue('status') as AdminProduct['status'];
        const { variant, label } = getStatusBadge(status);
        return <Badge variant={variant}>{label}</Badge>;
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'featured',
      header: 'Nổi bật',
      cell: ({ row }) => (
        <Checkbox
          checked={row.getValue('featured')}
          onCheckedChange={(value) => 
            onFeaturedToggle?.(row.original.id, !!value)
          }
          aria-label="Sản phẩm nổi bật"
        />
      ),
    },
    {
      accessorKey: 'rating',
      header: 'Đánh giá',
      cell: ({ row }) => (
        <div className="text-center">
          <div className="font-medium">{row.getValue('rating')}</div>
          <div className="text-xs text-muted-foreground">
            {row.original.reviews.toLocaleString('vi-VN')} đánh giá
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'sold',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-medium"
        >
          Đã bán
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-center font-medium">
          {(row.getValue('sold') as number).toLocaleString('vi-VN')}
        </div>
      ),
    },
    {
      accessorKey: 'createdAt',
      header: 'Ngày tạo',
      cell: ({ row }) => (
        <div className="text-sm">
          {new Date(row.getValue('createdAt')).toLocaleDateString('vi-VN')}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Hành động',
      cell: ({ row }) => {
        const product = row.original;
        
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Mở menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Hành động</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(product.id)}>
                Sao chép ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onEdit?.(product)}>
                <Edit className="mr-2 h-4 w-4" />
                Chỉnh sửa
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => window.open(product.affiliateLink, '_blank')}>
                <Eye className="mr-2 h-4 w-4" />
                Xem sản phẩm
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onStatusChange?.(product.id, 'active')}
                disabled={product.status === 'active'}
              >
                Kích hoạt
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onStatusChange?.(product.id, 'inactive')}
                disabled={product.status === 'inactive'}
              >
                Vô hiệu hóa
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete?.(product.id)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Xóa
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const table = useReactTable({
    data: products,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: 'includesString',
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectedProductIds = selectedRows.map(row => row.original.id);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          {/* Global Search */}
          <div className="relative max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Tìm kiếm sản phẩm..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* Status Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Trạng thái
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => table.getColumn('status')?.setFilterValue(undefined)}>
                Tất cả
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => table.getColumn('status')?.setFilterValue(['active'])}>
                Hoạt động
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => table.getColumn('status')?.setFilterValue(['inactive'])}>
                Không hoạt động
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => table.getColumn('status')?.setFilterValue(['pending'])}>
                Chờ duyệt
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          {/* Bulk Actions */}
          {selectedRows.length > 0 && (
            <>
              <Button
                variant="outline"
                onClick={() => onExport?.(selectedProductIds)}
              >
                <Download className="mr-2 h-4 w-4" />
                Xuất ({selectedRows.length})
              </Button>
              <Button
                variant="destructive"
                onClick={() => onBulkDelete?.(selectedProductIds)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Xóa ({selectedRows.length})
              </Button>
            </>
          )}
          
          {/* Regular Actions */}
          {onImport && (
            <Button variant="outline" onClick={onImport}>
              <Upload className="mr-2 h-4 w-4" />
              Nhập
            </Button>
          )}
          {onExport && (
            <Button variant="outline" onClick={() => onExport()}>
              <Download className="mr-2 h-4 w-4" />
              Xuất tất cả
            </Button>
          )}
          {onAdd && (
            <Button onClick={onAdd}>
              <Plus className="mr-2 h-4 w-4" />
              Thêm sản phẩm
            </Button>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Không có sản phẩm nào.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {selectedRows.length} trong {table.getFilteredRowModel().rows.length} hàng được chọn.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Số hàng mỗi trang</p>
            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => table.setPageSize(Number(e.target.value))}
              className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Trang {table.getState().pagination.pageIndex + 1} trong{" "}
            {table.getPageCount()}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Trang đầu</span>
              ⏮
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <span className="sr-only">Trang trước</span>
              ⏪
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Trang sau</span>
              ⏩
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <span className="sr-only">Trang cuối</span>
              ⏭
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

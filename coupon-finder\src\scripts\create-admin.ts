import { AuthService } from '@/lib/auth'
import { getDb } from '@/db'

async function createAdminUser() {
  try {
    const db = getDb()
    const authService = new AuthService(db)

    // Admin credentials
    const adminEmail = '<EMAIL>'
    const adminPassword = 'admin123456' // Change this in production!

    console.log('Creating admin user...')

    // Create admin user
    const adminUser = await authService.createUser(adminEmail, adminPassword, 'admin')

    if (adminUser) {
      console.log('✅ Admin user created successfully!')
      console.log('📧 Email:', adminEmail)
      console.log('🔑 Password:', adminPassword)
      console.log('⚠️  Please change the password after first login!')
    } else {
      console.log('❌ Failed to create admin user. Email might already exist.')
    }
  } catch (error) {
    console.error('❌ Error creating admin user:', error)
  }
}

// Run the script
createAdminUser()

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

// Types cho UI Store
export type Theme = 'light' | 'dark' | 'system'
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

export interface Modal {
  id: string
  component: string
  props?: Record<string, any>
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
}

export interface UIState {
  // Theme
  theme: Theme
  
  // Loading states
  globalLoading: boolean
  loadingStates: Record<string, boolean>
  
  // Notifications
  notifications: Notification[]
  
  // Modals
  modals: Modal[]
  
  // Sidebar
  sidebarOpen: boolean
  sidebarCollapsed: boolean
  
  // Mobile
  isMobile: boolean
  
  // Search
  searchOpen: boolean
  
  // Comparison
  comparisonPanelOpen: boolean
  
  // Actions
  setTheme: (theme: Theme) => void
  setGlobalLoading: (loading: boolean) => void
  setLoading: (key: string, loading: boolean) => void
  addNotification: (notification: Omit<Notification, 'id'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  openModal: (modal: Omit<Modal, 'id'>) => void
  closeModal: (id: string) => void
  closeAllModals: () => void
  setSidebarOpen: (open: boolean) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleSidebar: () => void
  setIsMobile: (isMobile: boolean) => void
  setSearchOpen: (open: boolean) => void
  toggleSearch: () => void
  setComparisonPanelOpen: (open: boolean) => void
  toggleComparisonPanel: () => void
  
  // Computed
  getLoadingState: (key: string) => boolean
  hasNotifications: () => boolean
  hasModals: () => boolean
}

// UI Store
export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state - Force light theme
      theme: 'light',
      globalLoading: false,
      loadingStates: {},
      notifications: [],
      modals: [],
      sidebarOpen: true,
      sidebarCollapsed: false,
      isMobile: false,
      searchOpen: false,
      comparisonPanelOpen: false,

      // Actions
      setTheme: (_theme: Theme) => {
        // Force light theme always - ignore any dark theme requests
        set({ theme: 'light' })

        // Always remove dark class to ensure light mode
        document.documentElement.classList.remove('dark')
      },

      setGlobalLoading: (loading: boolean) => {
        set({ globalLoading: loading })
      },

      setLoading: (key: string, loading: boolean) => {
        const { loadingStates } = get()
        set({
          loadingStates: {
            ...loadingStates,
            [key]: loading
          }
        })
      },

      addNotification: (notification: Omit<Notification, 'id'>) => {
        const { notifications } = get()
        const id = Date.now().toString()
        const newNotification: Notification = {
          ...notification,
          id,
          duration: notification.duration || 5000
        }
        
        set({
          notifications: [...notifications, newNotification]
        })

        // Auto remove notification after duration
        if (newNotification.duration && newNotification.duration > 0) {
          setTimeout(() => {
            get().removeNotification(id)
          }, newNotification.duration)
        }
      },

      removeNotification: (id: string) => {
        const { notifications } = get()
        set({
          notifications: notifications.filter(n => n.id !== id)
        })
      },

      clearNotifications: () => {
        set({ notifications: [] })
      },

      openModal: (modal: Omit<Modal, 'id'>) => {
        const { modals } = get()
        const id = Date.now().toString()
        const newModal: Modal = {
          ...modal,
          id,
          size: modal.size || 'md',
          closable: modal.closable !== false
        }
        
        set({
          modals: [...modals, newModal]
        })
      },

      closeModal: (id: string) => {
        const { modals } = get()
        set({
          modals: modals.filter(m => m.id !== id)
        })
      },

      closeAllModals: () => {
        set({ modals: [] })
      },

      setSidebarOpen: (open: boolean) => {
        set({ sidebarOpen: open })
      },

      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed })
      },

      toggleSidebar: () => {
        const { sidebarOpen } = get()
        set({ sidebarOpen: !sidebarOpen })
      },

      setIsMobile: (isMobile: boolean) => {
        set({ isMobile })
        
        // Auto close sidebar on mobile
        if (isMobile) {
          set({ sidebarOpen: false })
        }
      },

      setSearchOpen: (open: boolean) => {
        set({ searchOpen: open })
      },

      toggleSearch: () => {
        const { searchOpen } = get()
        set({ searchOpen: !searchOpen })
      },

      setComparisonPanelOpen: (open: boolean) => {
        set({ comparisonPanelOpen: open })
      },

      toggleComparisonPanel: () => {
        const { comparisonPanelOpen } = get()
        set({ comparisonPanelOpen: !comparisonPanelOpen })
      },

      // Computed properties
      getLoadingState: (key: string) => {
        const { loadingStates } = get()
        return loadingStates[key] || false
      },

      hasNotifications: () => {
        return get().notifications.length > 0
      },

      hasModals: () => {
        return get().modals.length > 0
      },
    }),
    {
      name: 'ui-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
      }),
    }
  )
)

// Selectors cho performance optimization
export const useTheme = () => useUIStore((state) => state.theme)
export const useGlobalLoading = () => useUIStore((state) => state.globalLoading)
export const useNotifications = () => useUIStore((state) => state.notifications)
export const useModals = () => useUIStore((state) => state.modals)
export const useSidebarOpen = () => useUIStore((state) => state.sidebarOpen)
export const useSidebarCollapsed = () => useUIStore((state) => state.sidebarCollapsed)
export const useIsMobile = () => useUIStore((state) => state.isMobile)
export const useSearchOpen = () => useUIStore((state) => state.searchOpen)
export const useComparisonPanelOpen = () => useUIStore((state) => state.comparisonPanelOpen)

// Actions selectors
export const useUIActions = () => useUIStore((state) => ({
  setTheme: state.setTheme,
  setGlobalLoading: state.setGlobalLoading,
  setLoading: state.setLoading,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications,
  openModal: state.openModal,
  closeModal: state.closeModal,
  closeAllModals: state.closeAllModals,
  setSidebarOpen: state.setSidebarOpen,
  setSidebarCollapsed: state.setSidebarCollapsed,
  toggleSidebar: state.toggleSidebar,
  setIsMobile: state.setIsMobile,
  setSearchOpen: state.setSearchOpen,
  toggleSearch: state.toggleSearch,
  setComparisonPanelOpen: state.setComparisonPanelOpen,
  toggleComparisonPanel: state.toggleComparisonPanel,
}))

// Utility hooks
export const useLoadingState = (key: string) => useUIStore((state) => state.getLoadingState(key))

// Notification helpers
export const useNotify = () => {
  const addNotification = useUIStore((state) => state.addNotification)
  
  return {
    success: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'success', title, message, duration }),
    error: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'error', title, message, duration }),
    warning: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'warning', title, message, duration }),
    info: (title: string, message: string, duration?: number) =>
      addNotification({ type: 'info', title, message, duration }),
  }
}

import { createFileRoute, Outlet, redirect } from '@tanstack/react-router';
import { useAuth } from '@/lib/auth-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  Package,
  Tag,
  Users,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  Bell,
} from 'lucide-react';

export const Route = createFileRoute('/admin')({
  component: AdminLayout,
});

function AdminLayout() {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Show loading while checking auth
  if (isLoading) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-32 w-32 border-b-2 border-primary'></div>
          <p className='mt-4 text-gray-600'><PERSON><PERSON> kiểm tra xác thực...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    window.location.href = `/login?redirect=${  encodeURIComponent('/admin')}`;
    return null;
  }

  // Redirect to home if not admin
  if (user?.role !== 'admin') {
    window.location.href = '/';
    return null;
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Admin Header */}
      <header className='bg-white shadow-sm border-b'>
        <div className='flex items-center justify-between px-6 py-4'>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm' className='lg:hidden'>
              <Menu className='h-5 w-5' />
            </Button>
            <h1 className='text-xl font-semibold text-gray-900'>
              Admin Dashboard
            </h1>
          </div>
          <div className='flex items-center gap-4'>
            <Button variant='ghost' size='sm'>
              <Bell className='h-5 w-5' />
            </Button>
            <div className='flex items-center gap-2'>
              <div className='w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-medium'>
                A
              </div>
              <span className='text-sm font-medium text-gray-700'>Admin</span>
            </div>
            <Button variant='ghost' size='sm'>
              <LogOut className='h-5 w-5' />
            </Button>
          </div>
        </div>
      </header>

      <div className='flex'>
        {/* Sidebar */}
        <aside className='hidden lg:block w-64 bg-white shadow-sm border-r min-h-screen'>
          <nav className='p-4 space-y-2'>
            <AdminNavItem
              icon={<LayoutDashboard className='h-5 w-5' />}
              label='Tổng quan'
              href='/admin'
              isActive={true}
            />
            <AdminNavItem
              icon={<Package className='h-5 w-5' />}
              label='Quản lý sản phẩm'
              href='/admin/products'
            />
            <AdminNavItem
              icon={<Tag className='h-5 w-5' />}
              label='Quản lý mã giảm giá'
              href='/admin/coupons'
            />
            <AdminNavItem
              icon={<Users className='h-5 w-5' />}
              label='Người dùng'
              href='/admin/users'
            />
            <AdminNavItem
              icon={<BarChart3 className='h-5 w-5' />}
              label='Thống kê'
              href='/admin/analytics'
            />
            <AdminNavItem
              icon={<Settings className='h-5 w-5' />}
              label='Cài đặt'
              href='/admin/settings'
            />
          </nav>
        </aside>

        {/* Main Content */}
        <main className='flex-1 p-6'>
          <AdminDashboard />
        </main>
      </div>
    </div>
  );
}

function AdminNavItem({
  icon,
  label,
  href,
  isActive = false,
}: {
  icon: React.ReactNode;
  label: string;
  href: string;
  isActive?: boolean;
}) {
  return (
    <a
      href={href}
      className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
        isActive
          ? 'bg-primary text-primary-foreground'
          : 'text-gray-700 hover:bg-gray-100'
      }`}
    >
      {icon}
      {label}
    </a>
  );
}

function AdminDashboard() {
  return (
    <div className='space-y-6'>
      {/* Page Header */}
      <div>
        <h2 className='text-2xl font-bold text-gray-900'>Tổng quan</h2>
        <p className='text-gray-600 mt-1'>
          Xem tổng quan về hiệu suất và hoạt động của hệ thống
        </p>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <StatsCard
          title='Tổng sản phẩm'
          value='12,345'
          change='+12%'
          changeType='positive'
          icon={<Package className='h-6 w-6' />}
        />
        <StatsCard
          title='Mã giảm giá'
          value='1,234'
          change='+8%'
          changeType='positive'
          icon={<Tag className='h-6 w-6' />}
        />
        <StatsCard
          title='Người dùng'
          value='45,678'
          change='+15%'
          changeType='positive'
          icon={<Users className='h-6 w-6' />}
        />
        <StatsCard
          title='Doanh thu'
          value='₫123M'
          change='-3%'
          changeType='negative'
          icon={<BarChart3 className='h-6 w-6' />}
        />
      </div>

      {/* Charts and Tables */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Hoạt động gần đây</CardTitle>
            <CardDescription>
              Các hoạt động mới nhất trong hệ thống
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {recentActivities.map((activity, index) => (
                <div
                  key={index}
                  className='flex items-center gap-3 p-3 bg-gray-50 rounded-lg'
                >
                  <div className='w-2 h-2 bg-blue-500 rounded-full' />
                  <div className='flex-1'>
                    <p className='text-sm font-medium text-gray-900'>
                      {activity.action}
                    </p>
                    <p className='text-xs text-gray-500'>{activity.time}</p>
                  </div>
                  <Badge variant='secondary' className='text-xs'>
                    {activity.type}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Products */}
        <Card>
          <CardHeader>
            <CardTitle>Sản phẩm hàng đầu</CardTitle>
            <CardDescription>
              Sản phẩm có lượt xem cao nhất tuần này
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {topProducts.map((product, index) => (
                <div key={index} className='flex items-center gap-3'>
                  <div className='w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center text-sm font-bold text-gray-600'>
                    {index + 1}
                  </div>
                  <div className='flex-1'>
                    <p className='text-sm font-medium text-gray-900 line-clamp-1'>
                      {product.name}
                    </p>
                    <p className='text-xs text-gray-500'>
                      {product.views.toLocaleString('vi-VN')} lượt xem
                    </p>
                  </div>
                  <Badge variant='outline' className='text-xs'>
                    {product.category}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Thao tác nhanh</CardTitle>
          <CardDescription>
            Các tác vụ thường dùng trong quản trị
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <Button className='h-20 flex-col gap-2'>
              <Package className='h-6 w-6' />
              Thêm sản phẩm mới
            </Button>
            <Button variant='outline' className='h-20 flex-col gap-2'>
              <Tag className='h-6 w-6' />
              Tạo mã giảm giá
            </Button>
            <Button variant='outline' className='h-20 flex-col gap-2'>
              <BarChart3 className='h-6 w-6' />
              Xem báo cáo
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function StatsCard({
  title,
  value,
  change,
  changeType,
  icon,
}: {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative';
  icon: React.ReactNode;
}) {
  return (
    <Card>
      <CardContent className='p-6'>
        <div className='flex items-center justify-between'>
          <div>
            <p className='text-sm font-medium text-gray-600'>{title}</p>
            <p className='text-2xl font-bold text-gray-900 mt-1'>{value}</p>
            <p
              className={`text-sm mt-1 ${
                changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {change} so với tháng trước
            </p>
          </div>
          <div className='text-gray-400'>{icon}</div>
        </div>
      </CardContent>
    </Card>
  );
}

// Mock data for development
const recentActivities = [
  {
    action: 'Thêm sản phẩm mới: iPhone 15 Pro Max',
    time: '5 phút trước',
    type: 'Sản phẩm',
  },
  {
    action: 'Cập nhật mã giảm giá SAVE50K',
    time: '15 phút trước',
    type: 'Mã giảm giá',
  },
  {
    action: 'Người dùng mới đăng ký',
    time: '30 phút trước',
    type: 'Người dùng',
  },
  {
    action: 'Xóa sản phẩm hết hàng',
    time: '1 giờ trước',
    type: 'Sản phẩm',
  },
];

const topProducts = [
  {
    name: 'iPhone 15 Pro Max 256GB',
    views: 15420,
    category: 'Điện tử',
  },
  {
    name: 'Samsung Galaxy S24 Ultra',
    views: 12350,
    category: 'Điện tử',
  },
  {
    name: 'MacBook Air M2',
    views: 9870,
    category: 'Laptop',
  },
  {
    name: 'AirPods Pro 2',
    views: 8920,
    category: 'Phụ kiện',
  },
  {
    name: 'iPad Pro 11 inch',
    views: 7650,
    category: 'Tablet',
  },
];

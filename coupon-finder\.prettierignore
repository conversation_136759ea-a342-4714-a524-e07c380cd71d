# Dependencies
node_modules/

# Build outputs
**/build
**/dist
.vinxi/
.wrangler/

# Public assets
**/public

# Generated files
pnpm-lock.yaml
routeTree.gen.ts
*.generated.*
*.gen.*

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache directories
.cache
.parcel-cache
.eslintcache

# Coverage
coverage/
*.lcov
.nyc_output

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
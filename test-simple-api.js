/**
 * Simple test để kiểm tra từng endpoint một
 */

const BASE_URL = 'http://localhost:3000/api';

async function testEndpoint(path, description) {
  console.log(`\n📡 Testing: ${description}`);
  console.log(`   URL: ${BASE_URL}${path}`);
  
  try {
    const response = await fetch(`${BASE_URL}${path}`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Success!`);
      console.log(`   📄 Response:`, JSON.stringify(data, null, 2).substring(0, 300) + '...');
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`   💥 Exception: ${error.message}`);
  }
}

async function runTests() {
  console.log('🔍 Testing API Endpoints...');
  
  // Test basic endpoints
  await testEndpoint('/test', 'Basic test endpoint');
  await testEndpoint('/health', 'Health check endpoint');
  
  // Test main endpoints
  await testEndpoint('/coupons', 'Coupons endpoint');
  await testEndpoint('/products', 'Products endpoint');
  await testEndpoint('/campaigns', 'Campaigns endpoint');
  
  // Test with search endpoint
  await testEndpoint('/coupons/search?keyword=test', 'Coupons search endpoint');
  
  console.log('\n🎉 Testing completed!');
}

runTests().catch(console.error);

# Khắc phục lỗi "Maximum update depth exceeded" trong React

## 🚨 Mô tả lỗi

```
Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
```

## 🔍 Nguyên nhân chính

### 1. **Dependency Array không ổn định trong useMemo**
- Các Zustand actions được include trong dependency array của `useMemo`
- Mặc dù Zustand actions thường stable, nhưng trong một số trường hợp có thể thay đổi reference

### 2. **useEffect với dependencies không ổn định**
- Zustand actions được include/exclude không nhất quán trong dependency arrays
- Gây ra re-render liên tục khi dependencies thay đổi

### 3. **Circular dependencies giữa Context và Store**
- Auth Context phụ thuộc vào Auth Store
- Query hooks cập nhật Auth Store
- Tạo ra vòng lặp vô tận

## 🛠️ Giải pháp đã áp dụng

### 1. **Cải thiện Auth Context (auth-context.tsx)**

```typescript
// ❌ TRƯỚC: Include Zustand actions trong dependency array
const contextValue = useMemo(() => ({...}), [
  user, isAuthenticated, ..., 
  openLoginModal, closeLoginModal, // ← Có thể gây infinite loop
])

// ✅ SAU: Loại bỏ Zustand actions khỏi dependency array
const contextValue = useMemo(() => ({...}), [
  user, isAuthenticated, ...,
  // REMOVED: Zustand UI actions are stable references
])
```

### 2. **Cải thiện Query Hooks (query-hooks.ts)**

```typescript
// ❌ TRƯỚC: Không include Zustand actions trong deps
React.useEffect(() => {
  if (mutation.isSuccess) {
    setUser(data.user);
    closeLoginModal();
  }
}, [mutation.isSuccess, mutation.data]); // ← Thiếu setUser, closeLoginModal

// ✅ SAU: Include tất cả Zustand actions để đảm bảo stability
React.useEffect(() => {
  if (mutation.isSuccess) {
    setUser(data.user);
    closeLoginModal();
  }
}, [mutation.isSuccess, mutation.data, setUser, closeLoginModal]); // ← Đầy đủ deps
```

### 3. **Cải thiện Auth Store (auth-store.ts)**

```typescript
// ❌ TRƯỚC: Return object mới mỗi lần
export const useAuthActions = () => useAuthStore((state) => ({
  setUser: state.setUser,
  clearUser: state.clearUser,
  // ... object mới mỗi lần render
}))

// ✅ SAU: Individual selectors để đảm bảo stable references
export const useAuthActions = () => {
  const setUser = useAuthStore((state) => state.setUser)
  const clearUser = useAuthStore((state) => state.clearUser)
  // ... individual selectors
  
  return { setUser, clearUser, ... }
}
```

## 📋 Checklist khắc phục

- [x] **Auth Context**: Loại bỏ Zustand actions khỏi useMemo dependencies
- [x] **Query Hooks**: Include đầy đủ Zustand actions trong useEffect dependencies  
- [x] **Auth Store**: Sử dụng individual selectors thay vì object destructuring
- [x] **Comments**: Thêm comments giải thích tại sao loại bỏ/include dependencies

## 🎯 Best Practices để tránh infinite loops

### 1. **Dependency Arrays**
```typescript
// ✅ Luôn include tất cả dependencies được sử dụng trong effect
useEffect(() => {
  doSomething(value, callback);
}, [value, callback]); // Include cả value và callback

// ❌ Không bỏ qua dependencies
useEffect(() => {
  doSomething(value, callback);
}, [value]); // Thiếu callback → có thể gây stale closure
```

### 2. **Zustand Actions**
```typescript
// ✅ Zustand actions thường stable, nhưng vẫn nên include để an toàn
const { setUser } = useAuthActions();
useEffect(() => {
  setUser(newUser);
}, [newUser, setUser]); // Include setUser để đảm bảo

// ✅ Hoặc sử dụng useCallback nếu cần
const handleSetUser = useCallback((user) => {
  setUser(user);
}, [setUser]);
```

### 3. **useMemo Dependencies**
```typescript
// ✅ Chỉ include values thực sự thay đổi
const memoizedValue = useMemo(() => ({
  data,
  actions: stableActions, // Stable references không cần include
}), [data]); // Chỉ include data

// ❌ Không include stable references
const memoizedValue = useMemo(() => ({
  data,
  actions: stableActions,
}), [data, stableActions]); // stableActions không cần thiết
```

## 🔧 Tools để debug

### 1. **React DevTools Profiler**
- Theo dõi re-renders
- Xác định components render quá nhiều lần

### 2. **Console logs**
```typescript
useEffect(() => {
  console.log('Effect triggered:', { user, isAuthenticated });
  // ... effect logic
}, [user, isAuthenticated]);
```

### 3. **ESLint Rules**
```json
{
  "rules": {
    "react-hooks/exhaustive-deps": "error"
  }
}
```

## 📚 Tài liệu tham khảo

- [React useEffect Dependencies](https://react.dev/reference/react/useEffect#specifying-reactive-dependencies)
- [Zustand Best Practices](https://github.com/pmndrs/zustand#best-practices)
- [TanStack Query Error Handling](https://tanstack.com/query/latest/docs/react/guides/query-retries)

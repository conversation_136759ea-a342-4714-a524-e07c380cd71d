import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Search, Filter, Grid, List } from 'lucide-react'
import { CouponCard } from '@/components/coupon-card'
import { Button } from '@/components/ui/button'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { api } from '@/api/hono-client'
import { parseShopeeUrl } from '@/lib/url-parser'

// Define search params type
interface SearchParams {
  q: string
  type: 'url' | 'keyword'
  category?: string
  sortBy: 'discount' | 'expiry' | 'popularity' | 'newest'
  page?: number
}

export const Route = createFileRoute('/search-results')({
  component: SearchResults,
  validateSearch: (search: Record<string, unknown>): SearchParams => ({
    q: (search.q as string) || '',
    type: (search.type as 'url' | 'keyword') || 'keyword',
    category: search.category as string,
    sortBy: (search.sortBy as 'discount' | 'expiry' | 'popularity' | 'newest') || 'discount',
    page: Number(search.page) || 1,
  }),
})

function SearchResults() {
  const searchParams = useSearch({ from: '/search-results' })
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [currentSort, setCurrentSort] = useState(searchParams.sortBy)
  const [currentCategory, setCurrentCategory] = useState(searchParams.category || 'all')

  // Fetch search results using TanStack Query và Hono API
  const {
    data: searchResults,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['coupon-search', searchParams.q, searchParams.type, currentCategory, currentSort, searchParams.page],
    queryFn: async () => {
      let apiParams: any = {
        category: currentCategory === 'all' ? '' : currentCategory,
        limit: 20,
        offset: ((searchParams.page || 1) - 1) * 20,
      }

      if (searchParams.type === 'url') {
        const urlInfo = parseShopeeUrl(searchParams.q)
        if (!urlInfo.isValid) {
          throw new Error('URL Shopee không hợp lệ')
        }
        
        apiParams.url = searchParams.q
        if (urlInfo.productName) {
          apiParams.keyword = urlInfo.productName
        }
      } else {
        apiParams.keyword = searchParams.q
      }

      const result = await api.coupons.search(apiParams)
      
      if (!result.success) {
        throw new Error(result.message || 'Không thể tìm kiếm mã giảm giá')
      }

      return result.data
    },
    enabled: !!searchParams.q,
  })

  const handleSortChange = (newSort: string) => {
    setCurrentSort(newSort as typeof currentSort)
  }

  const handleCategoryChange = (newCategory: string) => {
    setCurrentCategory(newCategory)
  }

  const totalResults = searchResults?.total || 0
  const coupons = searchResults?.coupons || []

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Kết quả tìm kiếm
              </h1>
              <p className="text-gray-600 mt-1">
                {searchParams.type === 'url' ? 'URL: ' : 'Từ khóa: '}
                <span className="font-medium">{searchParams.q}</span>
              </p>
            </div>
            
            {/* View Mode Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-64 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Bộ lọc
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Category Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Danh mục
                  </label>
                  <Select value={currentCategory} onValueChange={handleCategoryChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Tất cả danh mục" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả danh mục</SelectItem>
                      <SelectItem value="fashion">Thời trang</SelectItem>
                      <SelectItem value="electronics">Điện tử</SelectItem>
                      <SelectItem value="beauty">Làm đẹp</SelectItem>
                      <SelectItem value="home">Nhà cửa</SelectItem>
                      <SelectItem value="sports">Thể thao</SelectItem>
                      <SelectItem value="books">Sách</SelectItem>
                      <SelectItem value="food">Thực phẩm</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Sắp xếp theo
                  </label>
                  <Select value={currentSort} onValueChange={handleSortChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="discount">Giảm giá cao nhất</SelectItem>
                      <SelectItem value="expiry">Sắp hết hạn</SelectItem>
                      <SelectItem value="popularity">Phổ biến nhất</SelectItem>
                      <SelectItem value="newest">Mới nhất</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Search Info */}
            {searchParams.type === 'url' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Thông tin URL</CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const urlInfo = parseShopeeUrl(searchParams.q)
                    return (
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium">Trạng thái:</span>{' '}
                          <Badge variant={urlInfo.isValid ? 'success' : 'destructive'}>
                            {urlInfo.isValid ? 'Hợp lệ' : 'Không hợp lệ'}
                          </Badge>
                        </div>
                        {urlInfo.productName && (
                          <div>
                            <span className="font-medium">Sản phẩm:</span>{' '}
                            {urlInfo.productName}
                          </div>
                        )}
                        {urlInfo.productId && (
                          <div>
                            <span className="font-medium">ID:</span>{' '}
                            {urlInfo.productId}
                          </div>
                        )}
                      </div>
                    )
                  })()}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <h2 className="text-lg font-semibold">
                  {isLoading ? 'Đang tìm kiếm...' : `${totalResults} mã giảm giá`}
                </h2>
                {error && (
                  <Badge variant="destructive">
                    Có lỗi xảy ra
                  </Badge>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetch()}
                disabled={isLoading}
              >
                <Search className="h-4 w-4 mr-2" />
                Tìm lại
              </Button>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-6">
                      <div className="h-4 bg-gray-200 rounded mb-4"></div>
                      <div className="h-3 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Error State */}
            {error && (
              <Card className="text-center py-12">
                <CardContent>
                  <div className="text-red-500 text-lg mb-2">⚠️ Có lỗi xảy ra</div>
                  <p className="text-gray-600 mb-4">
                    {error instanceof Error ? error.message : 'Không thể tải kết quả tìm kiếm'}
                  </p>
                  <Button onClick={() => refetch()}>
                    Thử lại
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Results Grid */}
            {!isLoading && !error && coupons.length > 0 && (
              <div className={`grid gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                  : 'grid-cols-1'
              }`}>
                {coupons.map((coupon: any, index: number) => (
                  <CouponCard
                    key={coupon.id || index}
                    id={coupon.id}
                    title={coupon.title || coupon.name}
                    description={coupon.description || coupon.content}
                    discount={coupon.discount}
                    code={coupon.code}
                    expiryDate={coupon.expiryDate || coupon.end_time}
                    store={coupon.store || coupon.merchant}
                    category={coupon.category}
                    affiliateLink={coupon.affiliateLink || coupon.aff_link}
                    imageUrl={coupon.imageUrl || coupon.image}
                    isExpired={coupon.isExpired || (coupon.end_time && new Date(coupon.end_time) < new Date())}
                    className={viewMode === 'list' ? 'flex-row' : ''}
                    onCouponClick={(couponId) => {
                      console.log('Coupon clicked:', couponId);
                      // Additional tracking logic can be added here
                    }}
                  />
                ))}
              </div>
            )}

            {/* Empty State */}
            {!isLoading && !error && coupons.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-semibold mb-2">
                    Không tìm thấy mã giảm giá
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc
                  </p>
                  <Button variant="outline" onClick={() => window.history.back()}>
                    Quay lại
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

import { Hono } from 'hono'
import { jwt, sign } from 'hono/jwt'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import { eq } from 'drizzle-orm'
import { users } from '../../db/schema'
import type { Bindings, Variables } from '../hono-app'

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
})

// Create auth routes
export const authRoutes = new Hono<{
  Bindings: Bindings
  Variables: Variables
}>()

// Login endpoint
authRoutes.post('/login', 
  zValidator('json', loginSchema),
  async (c) => {
    try {
      const { email, password } = c.req.valid('json')
      const db = c.get('db')

      if (!db) {
        return c.json({
          error: 'Database unavailable',
          message: 'Database connection is not available'
        }, 503)
      }
      
      // Find user by email
      const user = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .get()
      
      if (!user) {
        return c.json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        }, 401)
      }
      
      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password)
      if (!isValidPassword) {
        return c.json({
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        }, 401)
      }
      
      // Generate JWT token
      const payload = {
        userId: user.id,
        email: user.email,
        role: user.role,
        exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 7), // 7 days
      }
      
      const token = await sign(payload, c.env.JWT_SECRET)
      
      // Store session in KV (optional)
      if (c.env.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          `session:${user.id}`,
          JSON.stringify({
            userId: user.id,
            email: user.email,
            role: user.role,
            loginAt: new Date().toISOString(),
          }),
          { expirationTtl: 60 * 60 * 24 * 7 } // 7 days
        )
      }
      
      return c.json({
        success: true,
        message: 'Login successful',
        data: {
          token,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
          }
        }
      })
      
    } catch (error) {
      console.error('Login error:', error)
      return c.json({
        error: 'Login failed',
        message: 'An error occurred during login'
      }, 500)
    }
  }
)

// Register endpoint
authRoutes.post('/register',
  zValidator('json', registerSchema),
  async (c) => {
    try {
      const { email, password, name } = c.req.valid('json')
      const db = c.get('db')

      if (!db) {
        return c.json({
          error: 'Database unavailable',
          message: 'Database connection is not available'
        }, 503)
      }
      
      // Check if user already exists
      const existingUser = await db
        .select()
        .from(users)
        .where(eq(users.email, email))
        .get()
      
      if (existingUser) {
        return c.json({
          error: 'User already exists',
          message: 'An account with this email already exists'
        }, 409)
      }
      
      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12)
      
      // Create user
      const userId = crypto.randomUUID()
      const newUser = await db
        .insert(users)
        .values({
          id: userId,
          email,
          password: hashedPassword,
          name: name || null,
          role: 'user',
        })
        .returning()
        .get()
      
      return c.json({
        success: true,
        message: 'Registration successful',
        data: {
          user: {
            id: newUser.id,
            email: newUser.email,
            name: newUser.name,
            role: newUser.role,
          }
        }
      }, 201)
      
    } catch (error) {
      console.error('Registration error:', error)
      return c.json({
        error: 'Registration failed',
        message: 'An error occurred during registration'
      }, 500)
    }
  }
)

// Protected route middleware
const authMiddleware = (c: any, next: any) => {
  const jwtMiddleware = jwt({
    secret: c.env.JWT_SECRET,
  })
  return jwtMiddleware(c, next)
}

// Get current user profile
authRoutes.get('/me', authMiddleware, async (c) => {
  try {
    const payload = c.get('jwtPayload')
    const db = c.get('db')

    if (!db) {
      return c.json({
        error: 'Database unavailable',
        message: 'Database connection is not available'
      }, 503)
    }
    
    const user = await db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        role: users.role,
        createdAt: users.createdAt,
      })
      .from(users)
      .where(eq(users.id, payload.userId))
      .get()
    
    if (!user) {
      return c.json({
        error: 'User not found',
        message: 'User account no longer exists'
      }, 404)
    }
    
    return c.json({
      success: true,
      data: { user }
    })
    
  } catch (error) {
    console.error('Get user error:', error)
    return c.json({
      error: 'Failed to get user',
      message: 'An error occurred while fetching user data'
    }, 500)
  }
})

// Logout endpoint
authRoutes.post('/logout', authMiddleware, async (c) => {
  try {
    const payload = c.get('jwtPayload')
    
    // Remove session from KV
    if (c.env.ENABLE_CACHING === 'true') {
      await c.env.CACHE.delete(`session:${payload.userId}`)
    }
    
    return c.json({
      success: true,
      message: 'Logout successful'
    })
    
  } catch (error) {
    console.error('Logout error:', error)
    return c.json({
      error: 'Logout failed',
      message: 'An error occurred during logout'
    }, 500)
  }
})

// Refresh token endpoint
authRoutes.post('/refresh', authMiddleware, async (c) => {
  try {
    const payload = c.get('jwtPayload')
    
    // Generate new token
    const newPayload = {
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
      exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 7), // 7 days
    }
    
    const newToken = await sign(newPayload, c.env.JWT_SECRET)
    
    return c.json({
      success: true,
      message: 'Token refreshed successfully',
      data: { token: newToken }
    })
    
  } catch (error) {
    console.error('Token refresh error:', error)
    return c.json({
      error: 'Token refresh failed',
      message: 'An error occurred while refreshing token'
    }, 500)
  }
})

export default authRoutes

import { Hono } from 'hono'
import { jwt } from 'hono/jwt'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Bindings, Variables } from '../hono-app'

// Validation schemas
const getAnalyticsSchema = z.object({
  type: z.enum(['clicks', 'searches', 'conversions', 'all']).default('all'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(1000)).default('100'),
})

// Create analytics routes
export const analyticsRoutes = new Hono<{
  Bindings: Bindings
  Variables: Variables
}>()

// JWT middleware for protected routes
const authMiddleware = (c: any, next: any) => {
  const jwtMiddleware = jwt({
    secret: c.env.JWT_SECRET,
  })
  return jwtMiddleware(c, next)
}

// Get analytics data (admin only)
analyticsRoutes.get('/',
  authMiddleware,
  zValidator('query', getAnalyticsSchema),
  async (c) => {
    try {
      const payload = c.get('jwtPayload')
      
      // Check if user is admin
      if (payload.role !== 'admin') {
        return c.json({
          error: 'Forbidden',
          message: 'Admin access required'
        }, 403)
      }
      
      const { type, startDate, endDate, limit } = c.req.valid('query')
      
      // Get analytics data from KV
      const analytics = await getAnalyticsFromKV(c, { type, startDate, endDate, limit })
      
      return c.json({
        success: true,
        message: 'Analytics data retrieved successfully',
        data: analytics,
      })
      
    } catch (error) {
      console.error('Get analytics error:', error)
      return c.json({
        error: 'Failed to get analytics',
        message: 'An error occurred while fetching analytics data'
      }, 500)
    }
  }
)

// Get analytics summary (admin only)
analyticsRoutes.get('/summary',
  authMiddleware,
  async (c) => {
    try {
      const payload = c.get('jwtPayload')
      
      // Check if user is admin
      if (payload.role !== 'admin') {
        return c.json({
          error: 'Forbidden',
          message: 'Admin access required'
        }, 403)
      }
      
      // Get summary data
      const summary = await getAnalyticsSummary(c)
      
      return c.json({
        success: true,
        message: 'Analytics summary retrieved successfully',
        data: summary,
      })
      
    } catch (error) {
      console.error('Get analytics summary error:', error)
      return c.json({
        error: 'Failed to get analytics summary',
        message: 'An error occurred while fetching analytics summary'
      }, 500)
    }
  }
)

// Helper functions
async function getAnalyticsFromKV(c: any, params: any) {
  const { type, startDate, endDate, limit } = params
  
  // List all analytics keys
  const listResult = await c.env.ANALYTICS.list({
    prefix: type === 'all' ? 'analytics:' : `analytics:${type}:`,
    limit,
  })
  
  const analytics = []
  
  for (const key of listResult.keys) {
    try {
      const data = await c.env.ANALYTICS.get(key.name)
      if (data) {
        const parsed = JSON.parse(data)
        
        // Filter by date range if provided
        if (startDate || endDate) {
          const eventDate = new Date(parsed.timestamp)
          if (startDate && eventDate < new Date(startDate)) continue
          if (endDate && eventDate > new Date(endDate)) continue
        }
        
        analytics.push(parsed)
      }
    } catch (error) {
      console.error('Error parsing analytics data:', error)
    }
  }
  
  return {
    events: analytics,
    total: analytics.length,
    hasMore: listResult.list_complete === false,
  }
}

async function getAnalyticsSummary(c: any) {
  // Get recent analytics data for summary
  const recentData = await getAnalyticsFromKV(c, {
    type: 'all',
    limit: 1000,
  })
  
  const events = recentData.events
  const now = new Date()
  const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  
  // Filter events by time periods
  const last24hEvents = events.filter(e => new Date(e.timestamp) > last24h)
  const last7dEvents = events.filter(e => new Date(e.timestamp) > last7d)
  
  // Calculate metrics
  const summary = {
    total: {
      events: events.length,
      searches: events.filter(e => e.type === 'coupon_search' || e.type === 'product_search').length,
      clicks: events.filter(e => e.type === 'coupon_click').length,
      comparisons: events.filter(e => e.type === 'product_comparison').length,
    },
    last24h: {
      events: last24hEvents.length,
      searches: last24hEvents.filter(e => e.type === 'coupon_search' || e.type === 'product_search').length,
      clicks: last24hEvents.filter(e => e.type === 'coupon_click').length,
      comparisons: last24hEvents.filter(e => e.type === 'product_comparison').length,
    },
    last7d: {
      events: last7dEvents.length,
      searches: last7dEvents.filter(e => e.type === 'coupon_search' || e.type === 'product_search').length,
      clicks: last7dEvents.filter(e => e.type === 'coupon_click').length,
      comparisons: last7dEvents.filter(e => e.type === 'product_comparison').length,
    },
    topKeywords: getTopKeywords(events),
    topCategories: getTopCategories(events),
  }
  
  return summary
}

function getTopKeywords(events: any[]) {
  const keywordCounts: Record<string, number> = {}
  
  events.forEach(event => {
    if (event.keyword) {
      keywordCounts[event.keyword] = (keywordCounts[event.keyword] || 0) + 1
    }
  })
  
  return Object.entries(keywordCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([keyword, count]) => ({ keyword, count }))
}

function getTopCategories(events: any[]) {
  const categoryCounts: Record<string, number> = {}
  
  events.forEach(event => {
    if (event.category) {
      categoryCounts[event.category] = (categoryCounts[event.category] || 0) + 1
    }
  })
  
  return Object.entries(categoryCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([category, count]) => ({ category, count }))
}

export default analyticsRoutes

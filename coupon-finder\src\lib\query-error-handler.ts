/**
 * TanStack Query Error Handling Utilities
 * 
 * Cung cấp error handling tích hợp với TanStack Query:
 * - Query error handlers
 * - Mutation error handlers
 * - Global error handling
 * - Error recovery strategies
 * - Toast notifications
 */

import { useQueryClient, type QueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { 
  ApiErrorClass, 
  type AppError, 
  parseUnknownError,
  isAuthError,
  isNetworkError,
  isValidationError,
  getErrorMessage
} from './api-error-handler';

// ===== ERROR HANDLING HOOKS =====

/**
 * Hook để handle query errors
 */
export function useQueryErrorHandler() {
  const queryClient = useQueryClient();

  const handleError = useCallback((error: unknown, queryKey?: string[]) => {
    const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));
    
    console.error('Query Error:', {
      queryKey,
      error: parsedError.toJSON(),
    });

    // Handle auth errors
    if (isAuthError(parsedError)) {
      handleAuthError(queryClient, parsedError);
      return;
    }

    // Handle network errors
    if (isNetworkError(parsedError)) {
      handleNetworkError(queryClient, parsedError);
      return;
    }

    // Show error notification
    showErrorNotification(parsedError);
  }, [queryClient]);

  return { handleError };
}

/**
 * Hook để handle mutation errors
 */
export function useMutationErrorHandler() {
  const queryClient = useQueryClient();

  const handleError = useCallback((error: unknown, variables?: any, context?: any) => {
    const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));
    
    console.error('Mutation Error:', {
      variables,
      context,
      error: parsedError.toJSON(),
    });

    // Handle validation errors
    if (isValidationError(parsedError)) {
      handleValidationError(parsedError);
      return;
    }

    // Handle auth errors
    if (isAuthError(parsedError)) {
      handleAuthError(queryClient, parsedError);
      return;
    }

    // Show error notification
    showErrorNotification(parsedError);
  }, [queryClient]);

  const handleSuccess = useCallback((data: any, variables?: any, context?: any) => {
    console.log('Mutation Success:', {
      variables,
      context,
      data,
    });

    // Show success notification if needed
    showSuccessNotification('Operation completed successfully');
  }, []);

  return { handleError, handleSuccess };
}

// ===== SPECIFIC ERROR HANDLERS =====

/**
 * Handle authentication errors
 */
function handleAuthError(queryClient: QueryClient, error: AppError) {
  console.warn('Authentication error detected:', error.message);
  
  // Clear auth-related queries
  queryClient.removeQueries({ queryKey: ['auth'] });
  
  // Redirect to login if specified
  if ('redirectTo' in error && error.redirectTo) {
    window.location.href = error.redirectTo;
  } else {
    // Default redirect to login
    window.location.href = '/login';
  }
  
  showErrorNotification(error, 'Authentication required. Please log in again.');
}

/**
 * Handle network errors
 */
function handleNetworkError(queryClient: QueryClient, error: AppError) {
  console.warn('Network error detected:', error.message);
  
  // Check if offline
  if ('isOffline' in error && error.isOffline) {
    showErrorNotification(error, 'You appear to be offline. Please check your connection.');
    return;
  }
  
  // Show retry option for network errors
  showErrorNotification(error, 'Network error occurred. Please try again.');
}

/**
 * Handle validation errors
 */
function handleValidationError(error: AppError) {
  console.warn('Validation error detected:', error.message);
  
  // Validation errors are usually handled by forms
  // Just log for debugging
  if ('fieldErrors' in error) {
    console.log('Field errors:', error.fieldErrors);
  }
}

// ===== NOTIFICATION HELPERS =====

/**
 * Show error notification
 * TODO: Integrate với toast library (react-hot-toast, sonner, etc.)
 */
function showErrorNotification(error: AppError, customMessage?: string) {
  const message = customMessage || getErrorMessage(error);
  
  // For now, use console.error
  // TODO: Replace với actual toast notification
  console.error('Error Notification:', message);
  
  // Browser notification as fallback
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification('Error', {
      body: message,
      icon: '/favicon.ico',
    });
  }
}

/**
 * Show success notification
 */
function showSuccessNotification(message: string) {
  // For now, use console.log
  // TODO: Replace với actual toast notification
  console.log('Success Notification:', message);
}

// ===== QUERY CLIENT ERROR HANDLERS =====

/**
 * Global error handler cho QueryClient
 */
export function createGlobalErrorHandler() {
  return {
    onError: (error: unknown, query: any) => {
      const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));
      
      console.error('Global Query Error:', {
        queryKey: query.queryKey,
        error: parsedError.toJSON(),
      });

      // Don't show notifications for background refetches
      if (query.state.fetchStatus === 'idle') {
        return;
      }

      // Handle specific error types
      if (isAuthError(parsedError)) {
        // Will be handled by individual query error handlers
        return;
      }

      if (isNetworkError(parsedError)) {
        // Will be handled by individual query error handlers
        return;
      }

      // Show generic error for other types
      showErrorNotification(parsedError);
    },
  };
}

/**
 * Global mutation error handler
 */
export function createGlobalMutationErrorHandler() {
  return {
    onError: (error: unknown, variables: any, context: any, mutation: any) => {
      const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));
      
      console.error('Global Mutation Error:', {
        mutationKey: mutation.options.mutationKey,
        variables,
        error: parsedError.toJSON(),
      });

      // Individual mutation error handlers will handle specific cases
    },
  };
}

// ===== ERROR RECOVERY UTILITIES =====

/**
 * Retry failed queries
 */
export function useRetryFailedQueries() {
  const queryClient = useQueryClient();

  const retryAll = useCallback(() => {
    queryClient.refetchQueries({
      type: 'active',
      stale: true,
    });
  }, [queryClient]);

  const retryQuery = useCallback((queryKey: string[]) => {
    queryClient.refetchQueries({ queryKey });
  }, [queryClient]);

  return { retryAll, retryQuery };
}

/**
 * Clear error state
 */
export function useClearErrors() {
  const queryClient = useQueryClient();

  const clearQueryErrors = useCallback((queryKey?: string[]) => {
    if (queryKey) {
      queryClient.resetQueries({ queryKey });
    } else {
      queryClient.resetQueries();
    }
  }, [queryClient]);

  const clearMutationErrors = useCallback(() => {
    queryClient.getMutationCache().clear();
  }, [queryClient]);

  return { clearQueryErrors, clearMutationErrors };
}

// ===== ERROR BOUNDARY INTEGRATION =====

/**
 * Error info cho React Error Boundary
 */
export interface ErrorBoundaryInfo {
  error: AppError;
  errorInfo: {
    componentStack: string;
    queryKey?: string[];
    mutationKey?: string[];
  };
  retry: () => void;
  reset: () => void;
}

/**
 * Create error boundary props
 */
export function createErrorBoundaryProps(queryClient: QueryClient) {
  return {
    onError: (error: Error, errorInfo: any) => {
      const parsedError = new ApiErrorClass(parseUnknownError(error));

      console.error('Error Boundary Caught:', {
        error: parsedError.toJSON(),
        errorInfo,
      });

      // Report error to monitoring service
      // TODO: Integrate với error monitoring (Sentry, LogRocket, etc.)
    },

    fallbackRender: ({ error, resetErrorBoundary }: any) => {
      const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));

      // Return error boundary configuration object instead of JSX
      return {
        error: parsedError,
        message: getErrorMessage(parsedError),
        code: parsedError.code,
        onRetry: resetErrorBoundary,
        onReload: () => window.location.reload(),
      };
    },
  };
}

/**
 * Error boundary fallback component factory
 * Returns a function that creates the error UI
 */
export function createErrorFallback() {
  return ({ error, resetErrorBoundary }: any) => {
    const parsedError = error instanceof ApiErrorClass ? error : new ApiErrorClass(parseUnknownError(error));

    // Return plain object for error boundary
    return {
      type: 'error-boundary',
      error: parsedError.toJSON(),
      message: getErrorMessage(parsedError),
      actions: {
        retry: resetErrorBoundary,
        reload: () => window.location.reload(),
      }
    };
  };
}

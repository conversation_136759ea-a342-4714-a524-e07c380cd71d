import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { eq, and, gt } from 'drizzle-orm'
import { users, sessions, type User, type Session, type NewUser, type NewSession } from '@/db/schema'
import type { DrizzleD1Database } from 'drizzle-orm/d1'

// JWT secret from environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production'
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds

export interface AuthUser {
  id: string
  email: string
  role: string
  isActive: boolean
  lastLoginAt: string | null
  createdAt: string
}

export interface AuthSession {
  id: string
  userId: string
  token: string
  expiresAt: string
  user?: AuthUser
}

export class AuthService {
  constructor(private db: DrizzleD1Database) {}

  // Hash password
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12)
  }

  // Verify password
  async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword)
  }

  // Generate JWT token
  generateToken(userId: string): string {
    return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '7d' })
  }

  // Verify JWT token
  verifyToken(token: string): { userId: string } | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }
      return decoded
    } catch {
      return null
    }
  }

  // Create user
  async createUser(email: string, password: string, role: string = 'user'): Promise<AuthUser | null> {
    try {
      const hashedPassword = await this.hashPassword(password)
      const userId = crypto.randomUUID()

      const newUser: NewUser = {
        id: userId,
        email,
        password: hashedPassword,
        role,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      await this.db.insert(users).values(newUser)

      // Return user without password
      const { password: _, ...userWithoutPassword } = newUser
      return userWithoutPassword as AuthUser
    } catch (error) {
      console.error('Error creating user:', error)
      return null
    }
  }

  // Authenticate user
  async authenticateUser(email: string, password: string): Promise<AuthUser | null> {
    try {
      const user = await this.db
        .select()
        .from(users)
        .where(and(eq(users.email, email), eq(users.isActive, true)))
        .get()

      if (!user) return null

      const isValidPassword = await this.verifyPassword(password, user.password)
      if (!isValidPassword) return null

      // Update last login
      await this.db
        .update(users)
        .set({ 
          lastLoginAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
        .where(eq(users.id, user.id))

      // Return user without password
      const { password: _, ...userWithoutPassword } = user
      return userWithoutPassword as AuthUser
    } catch (error) {
      console.error('Error authenticating user:', error)
      return null
    }
  }

  // Create session
  async createSession(userId: string): Promise<AuthSession | null> {
    try {
      const token = this.generateToken(userId)
      const sessionId = crypto.randomUUID()
      const expiresAt = new Date(Date.now() + SESSION_DURATION).toISOString()

      const newSession: NewSession = {
        id: sessionId,
        userId,
        token,
        expiresAt,
        createdAt: new Date().toISOString(),
      }

      await this.db.insert(sessions).values(newSession)

      return newSession as AuthSession
    } catch (error) {
      console.error('Error creating session:', error)
      return null
    }
  }

  // Get session with user
  async getSessionWithUser(token: string): Promise<AuthSession | null> {
    try {
      const decoded = this.verifyToken(token)
      if (!decoded) return null

      const session = await this.db
        .select({
          id: sessions.id,
          userId: sessions.userId,
          token: sessions.token,
          expiresAt: sessions.expiresAt,
          user: {
            id: users.id,
            email: users.email,
            role: users.role,
            isActive: users.isActive,
            lastLoginAt: users.lastLoginAt,
            createdAt: users.createdAt,
          }
        })
        .from(sessions)
        .innerJoin(users, eq(sessions.userId, users.id))
        .where(
          and(
            eq(sessions.token, token),
            gt(sessions.expiresAt, new Date().toISOString()),
            eq(users.isActive, true)
          )
        )
        .get()

      return session as AuthSession
    } catch (error) {
      console.error('Error getting session:', error)
      return null
    }
  }

  // Delete session (logout)
  async deleteSession(token: string): Promise<boolean> {
    try {
      await this.db.delete(sessions).where(eq(sessions.token, token))
      return true
    } catch (error) {
      console.error('Error deleting session:', error)
      return false
    }
  }

  // Clean expired sessions
  async cleanExpiredSessions(): Promise<void> {
    try {
      await this.db
        .delete(sessions)
        .where(gt(new Date().toISOString(), sessions.expiresAt))
    } catch (error) {
      console.error('Error cleaning expired sessions:', error)
    }
  }

  // Get user by ID
  async getUserById(userId: string): Promise<AuthUser | null> {
    try {
      const user = await this.db
        .select()
        .from(users)
        .where(and(eq(users.id, userId), eq(users.isActive, true)))
        .get()

      if (!user) return null

      const { password: _, ...userWithoutPassword } = user
      return userWithoutPassword as AuthUser
    } catch (error) {
      console.error('Error getting user by ID:', error)
      return null
    }
  }
}

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import * as React from 'react';
import { createQueryClient } from './query-client';

/**
 * QueryProvider component để wrap toàn bộ app với TanStack Query
 *
 * Component này:
 * - Tạo QueryClient instance duy nhất cho mỗi request (SSR safe)
 * - Cung cấp QueryClient cho toàn bộ component tree
 * - <PERSON><PERSON> gồm ReactQueryDevtools với enhanced configuration
 * - Tích hợp error boundary cho query errors
 * - T<PERSON>i <PERSON>u cho TanStack Start và SSR
 */

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  // Tạo QueryClient instance duy nhất cho mỗi component mount
  // Điều này quan trọng cho SSR để tránh hydration mismatch
  const [queryClient] = React.useState(() => createQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Enhanced devtools configuration cho development */}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools
          initialIsOpen={false}
          position="bottom-right"
          buttonPosition="bottom-right"
          toggleButtonProps={{
            style: {
              marginLeft: '5px',
              transform: 'scale(1)',
              transformOrigin: 'bottom right',
            },
          }}
          panelProps={{
            style: {
              height: '500px',
              fontSize: '12px',
            },
          }}
          closeButtonProps={{
            style: {
              color: '#ff6b6b',
            },
          }}
          errorTypes={[
            { name: 'API Errors', initialIsOpen: true },
            { name: 'Network Errors', initialIsOpen: true },
            { name: 'Validation Errors', initialIsOpen: false },
          ]}
        />
      )}
    </QueryClientProvider>
  );
}

// ❌ REMOVED: Custom useQueryClient hook
// Sử dụng useQueryClient từ @tanstack/react-query thay thế

/**
 * Higher-order component để wrap component với QueryProvider
 * Hữu ích cho testing hoặc standalone components
 */
export function withQueryProvider<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => (
    <QueryProvider>
      <Component {...props} />
    </QueryProvider>
  );
  
  WrappedComponent.displayName = `withQueryProvider(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Utility function để prefetch data trên server
 * Sử dụng trong TanStack Start loaders
 */
export async function prefetchQuery(
  queryClient: QueryClient,
  options: Parameters<QueryClient['prefetchQuery']>[0]
) {
  try {
    await queryClient.prefetchQuery(options);
  } catch (error) {
    // Log error nhưng không throw để không break SSR
    console.error('Failed to prefetch query:', error);
  }
}

/**
 * Utility function để dehydrate QueryClient state
 * Sử dụng cho SSR hydration
 */
export function dehydrateQueryClient(queryClient: QueryClient) {
  // Import dehydrate dynamically để tránh bundle size issues
  return import('@tanstack/react-query').then(({ dehydrate }) => 
    dehydrate(queryClient)
  );
}

/**
 * Utility function để hydrate QueryClient state
 * Sử dụng cho client-side hydration
 */
export function hydrateQueryClient(
  queryClient: QueryClient, 
  dehydratedState: any
) {
  // Import hydrate dynamically
  return import('@tanstack/react-query').then(({ hydrate }) => 
    hydrate(queryClient, dehydratedState)
  );
}

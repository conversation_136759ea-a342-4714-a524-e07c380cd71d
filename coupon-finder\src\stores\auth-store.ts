import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import type { AuthUser } from '@/lib/auth'

// Auth Store chỉ handle CLIENT STATE - không handle API calls
// Server state được handle bởi TanStack Query
export interface AuthClientState {
  // Client-only state
  user: AuthUser | null
  isAuthenticated: boolean
  rememberMe: boolean
  lastLoginEmail: string

  // UI state
  showLoginModal: boolean
  showRegisterModal: boolean

  // Actions (chỉ client state)
  setUser: (user: AuthUser | null) => void
  setRememberMe: (remember: boolean) => void
  setLastLoginEmail: (email: string) => void
  clearUser: () => void

  // UI actions
  openLoginModal: () => void
  closeLoginModal: () => void
  openRegisterModal: () => void
  closeRegisterModal: () => void

  // Computed
  isAdmin: () => boolean
}

// Auth Store với persist middleware - CHỈ CLIENT STATE
export const useAuthStore = create<AuthClientState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      rememberMe: false,
      lastLoginEmail: '',
      showLoginModal: false,
      showRegisterModal: false,

      // Client state actions
      setUser: (user: AuthUser | null) => {
        set({
          user,
          isAuthenticated: !!user,
        })
      },

      setRememberMe: (remember: boolean) => {
        set({ rememberMe: remember })
      },

      setLastLoginEmail: (email: string) => {
        set({ lastLoginEmail: email })
      },

      clearUser: () => {
        set({
          user: null,
          isAuthenticated: false,
        })
      },

      // UI actions
      openLoginModal: () => {
        set({ showLoginModal: true, showRegisterModal: false })
      },

      closeLoginModal: () => {
        set({ showLoginModal: false })
      },

      openRegisterModal: () => {
        set({ showRegisterModal: true, showLoginModal: false })
      },

      closeRegisterModal: () => {
        set({ showRegisterModal: false })
      },

      // Computed properties
      isAdmin: () => {
        const { user } = get()
        return user?.role === 'admin'
      },
    }),
    {
      name: 'auth-client-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        rememberMe: state.rememberMe,
        lastLoginEmail: state.lastLoginEmail,
      }),
    }
  )
)

// Selectors cho performance optimization - CHỈ CLIENT STATE
export const useUser = () => useAuthStore((state) => state.user)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useIsAdmin = () => useAuthStore((state) => state.isAdmin())
export const useRememberMe = () => useAuthStore((state) => state.rememberMe)
export const useLastLoginEmail = () => useAuthStore((state) => state.lastLoginEmail)

// UI selectors
export const useAuthModals = () => useAuthStore((state) => ({
  showLoginModal: state.showLoginModal,
  showRegisterModal: state.showRegisterModal,
}))

// Actions selectors - CHỈ CLIENT ACTIONS với stable references
export const useAuthActions = () => {
  const setUser = useAuthStore((state) => state.setUser)
  const clearUser = useAuthStore((state) => state.clearUser)
  const setRememberMe = useAuthStore((state) => state.setRememberMe)
  const setLastLoginEmail = useAuthStore((state) => state.setLastLoginEmail)
  const openLoginModal = useAuthStore((state) => state.openLoginModal)
  const closeLoginModal = useAuthStore((state) => state.closeLoginModal)
  const openRegisterModal = useAuthStore((state) => state.openRegisterModal)
  const closeRegisterModal = useAuthStore((state) => state.closeRegisterModal)

  // Return individual selectors để đảm bảo stable references
  return {
    setUser,
    clearUser,
    setRememberMe,
    setLastLoginEmail,
    openLoginModal,
    closeLoginModal,
    openRegisterModal,
    closeRegisterModal,
  }
}

// Combined hook cho Navigation component (deprecated - use useAuth from auth-context instead)
export const useAuthStore_DEPRECATED = () => {
  const user = useAuthStore((state) => state.user)
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const clearUser = useAuthStore((state) => state.clearUser)

  return {
    user,
    isAuthenticated,
    logout: clearUser,
  }
}

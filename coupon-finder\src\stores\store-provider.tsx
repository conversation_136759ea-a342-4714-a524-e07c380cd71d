import { useEffect, type ReactNode } from 'react'
import { initializeStores } from './index'

interface StoreProviderProps {
  children: ReactNode
}

/**
 * Store Provider component để initialize Zustand stores
 * Tích hợp với TanStack Start application
 */
export function StoreProvider({ children }: StoreProviderProps) {
  useEffect(() => {
    // Initialize all stores when app starts
    initializeStores()
  }, [])

  return <>{children}</>
}

// Hook để check if stores are ready
export function useStoresReady() {
  // Stores are always ready với Zustand (no async initialization needed)
  return true
}

// Development helpers
export function StoreDevtools() {
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-2">
      <div className="bg-black/80 text-white p-2 rounded text-xs">
        <div>Zustand Stores Active</div>
        <div className="text-gray-300">
          Auth • Products • Coupons • UI
        </div>
      </div>
    </div>
  )
}

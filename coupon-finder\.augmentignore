# Dependencies
node_modules/
.pnpm-store/
package-lock.json
yarn.lock

# Build outputs
dist/
build/
.vinxi/
.wrangler/
.output/
/build/
/api/
/server/build
/public/build

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.dev.vars

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Cache directories
.cache
.parcel-cache
.eslintcache

# Coverage
coverage/
*.lcov
.nyc_output

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Generated files
routeTree.gen.ts
*.generated.*
*.gen.*

# Deployment
.vercel

# Testing
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Sentry Config File
.env.sentry-build-plugin

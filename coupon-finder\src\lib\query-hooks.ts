import * as React from 'react';
import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
} from '@tanstack/react-query';
import { queryKeys, queryOptions } from './query-client';
import { useAuthActions } from '@/stores/auth-store';
import {
  useQueryError<PERSON>andler,
  useMutationErrorHandler,
  useRetryFailedQueries,
  useClearErrors
} from './query-error-handler';
import {
  apiRequest,
  withRetry,
  ApiErrorClass
} from './api-error-handler';

/**
 * Custom hooks cho TanStack Query với enhanced error handling
 *
 * Các hooks này cung cấp:
 * - Type-safe query và mutation functions
 * - Comprehensive error handling với custom error classes
 * - Automatic retry strategies
 * - Optimistic updates
 * - Cache invalidation strategies
 * - Integration với existing API routes
 * - Error recovery utilities
 */

// ============================================================================
// AUTH HOOKS
// ============================================================================

/**
 * Hook để fetch current user data - ENHANCED với Error Handling
 * Note: Không auto-sync với auth store để tránh infinite loop
 */
export function useCurrentUser(options?: { autoSync?: boolean }) {
  const { setUser, clearUser } = useAuthActions();
  const { handleError } = useQueryErrorHandler();
  const autoSync = options?.autoSync ?? false;

  const query = useQuery({
    ...queryOptions.auth.user(),
    retry: false, // Không retry cho auth queries
  });

  // Handle side effects với useEffect thay vì callbacks
  React.useEffect(() => {
    if (autoSync && query.isSuccess) {
      if (query.data?.user) {
        setUser(query.data.user);
      } else {
        clearUser();
      }
    }
  }, [autoSync, query.isSuccess, query.data, setUser, clearUser]); // Include Zustand actions để đảm bảo stability

  React.useEffect(() => {
    if (autoSync && query.isError) {
      clearUser();
      handleError(query.error, queryKeys.auth.user());
    } else if (query.isError) {
      handleError(query.error, queryKeys.auth.user());
    }
  }, [autoSync, query.isError, query.error, handleError, clearUser]); // Include clearUser để đảm bảo stability

  return query;
}

/**
 * Hook để login user - ENHANCED với Error Handling
 */
export function useLogin() {
  const queryClient = useQueryClient();
  const { setUser, setLastLoginEmail, setRememberMe, closeLoginModal } = useAuthActions();
  const { handleError, handleSuccess } = useMutationErrorHandler();

  const mutation = useMutation({
    mutationFn: async (credentials: { email: string; password: string; rememberMe?: boolean }) => {
      // Use Hono RPC client instead of apiRequest
      const { api } = await import('../api/hono-client');
      return await api.auth.login(credentials.email, credentials.password);
    },
  });

  // Handle side effects với useEffect thay vì callbacks
  React.useEffect(() => {
    if (mutation.isSuccess && mutation.data) {
      // Update server state cache
      queryClient.setQueryData(queryKeys.auth.user(), mutation.data);

      // Handle client state sync
      if (mutation.data.user) {
        setUser(mutation.data.user);
      }

      // Handle remember me functionality
      if (mutation.variables?.rememberMe) {
        setRememberMe(true);
        setLastLoginEmail(mutation.variables.email);
      }

      closeLoginModal();
      handleSuccess(mutation.data, mutation.variables);
    }
  }, [mutation.isSuccess, mutation.data, mutation.variables, queryClient, handleSuccess, setUser, setRememberMe, setLastLoginEmail, closeLoginModal]); // Include all Zustand actions

  React.useEffect(() => {
    if (mutation.isError && mutation.error) {
      handleError(mutation.error, mutation.variables);
    }
  }, [mutation.isError, mutation.error, mutation.variables, handleError]);

  return mutation;
}

/**
 * Hook để register user - ENHANCED với Error Handling
 */
export function useRegister() {
  const queryClient = useQueryClient();
  const { setUser, setLastLoginEmail, closeRegisterModal } = useAuthActions();
  const { handleError, handleSuccess } = useMutationErrorHandler();

  const mutation = useMutation({
    mutationFn: async (credentials: { email: string; password: string; name?: string }) => {
      // Use Hono RPC client instead of apiRequest
      const { api } = await import('../api/hono-client');
      return await api.auth.register(credentials.email, credentials.password, credentials.name);
    },
  });

  // Handle side effects với useEffect thay vì callbacks
  React.useEffect(() => {
    if (mutation.isSuccess && mutation.data) {
      // Update server state cache
      queryClient.setQueryData(queryKeys.auth.user(), mutation.data);

      // Handle client state sync
      if (mutation.data.user) {
        setUser(mutation.data.user);
        setLastLoginEmail(mutation.variables?.email || '');
      }

      closeRegisterModal();
      handleSuccess(mutation.data, mutation.variables);
    }
  }, [mutation.isSuccess, mutation.data, mutation.variables, queryClient, handleSuccess, setUser, setLastLoginEmail, closeRegisterModal]); // Include all Zustand actions

  React.useEffect(() => {
    if (mutation.isError && mutation.error) {
      handleError(mutation.error, mutation.variables);
    }
  }, [mutation.isError, mutation.error, mutation.variables, handleError]);

  return mutation;
}

/**
 * Hook để logout user - ENHANCED với Error Handling
 */
export function useLogout() {
  const queryClient = useQueryClient();
  const { clearUser } = useAuthActions();
  const { handleError, handleSuccess } = useMutationErrorHandler();

  const mutation = useMutation({
    mutationFn: async () => {
      // Use Hono RPC client instead of apiRequest
      const { api } = await import('../api/hono-client');
      // Note: We need to get the token from auth store
      const token = 'placeholder-token'; // TODO: Get from auth store
      return await api.auth.logout(token);
    },
  });

  // Handle side effects với useEffect thay vì callbacks
  React.useEffect(() => {
    if (mutation.isSuccess) {
      // Clear client state
      clearUser();

      // Clear tất cả server state queries khi logout
      queryClient.clear();

      handleSuccess(mutation.data);
    }
  }, [mutation.isSuccess, mutation.data, queryClient, handleSuccess, clearUser]); // Include clearUser để đảm bảo stability

  React.useEffect(() => {
    if (mutation.isError && mutation.error) {
      handleError(mutation.error);
    }
  }, [mutation.isError, mutation.error, handleError]);

  return mutation;
}

// ============================================================================
// COUPON HOOKS
// ============================================================================

/**
 * Hook để fetch danh sách coupons với filters
 */
export function useCoupons(filters: Record<string, any> = {}) {
  return useQuery({
    ...queryOptions.coupons.list(filters),
  });
}

/**
 * Hook để search coupons
 */
export function useSearchCoupons(query: string) {
  return useQuery({
    ...queryOptions.coupons.search(query),
  });
}

/**
 * Hook để fetch coupon detail
 */
export function useCoupon(id: string) {
  return useQuery({
    queryKey: queryKeys.coupons.detail(id),
    queryFn: async () => {
      const response = await fetch(`/api/coupons/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch coupon');
      }
      return response.json();
    },
    enabled: !!id,
  });
}

/**
 * Hook để create coupon (admin only)
 */
export function useCreateCoupon() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (couponData: any) => {
      // Use Hono RPC client instead of fetch
      const { api } = await import('../api/hono-client');
      // Note: This would need to be implemented in Hono admin routes
      // For now, we'll use a placeholder that throws an error
      throw new Error('Create coupon API not yet implemented in Hono routes');
    },
    onSuccess: () => {
      // Invalidate coupon lists
      queryClient.invalidateQueries({ queryKey: queryKeys.coupons.lists() });
    },
  });
}

// ============================================================================
// PRODUCT HOOKS
// ============================================================================

/**
 * Hook để search products
 */
export function useSearchProducts(query: string) {
  return useQuery({
    ...queryOptions.products.search(query),
  });
}

/**
 * Hook để compare products
 */
export function useCompareProducts(productIds: string[]) {
  return useQuery({
    ...queryOptions.products.compare(productIds),
  });
}

/**
 * Hook để fetch top selling products
 */
export function useTopSellingProducts() {
  return useQuery({
    queryKey: queryKeys.products.topSelling(),
    queryFn: async () => {
      // Use Hono RPC client instead of fetch
      const { api } = await import('../api/hono-client');
      return api.products.getTopSelling();
    },
    staleTime: 10 * 60 * 1000, // 10 phút
  });
}

// ============================================================================
// CAMPAIGN HOOKS
// ============================================================================

/**
 * Hook để fetch active campaigns
 */
export function useActiveCampaigns() {
  return useQuery({
    queryKey: queryKeys.campaigns.active(),
    queryFn: async () => {
      // Use Hono RPC client instead of fetch
      const { api } = await import('../api/hono-client');
      return api.campaigns.getAll({ status: 'active' });
    },
    staleTime: 5 * 60 * 1000, // 5 phút
  });
}

/**
 * Hook để fetch campaigns với filters
 */
export function useCampaigns(filters: Record<string, any> = {}) {
  return useQuery({
    queryKey: queryKeys.campaigns.list(filters),
    queryFn: async () => {
      const params = new URLSearchParams(filters);
      const response = await fetch(`/api/campaigns?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch campaigns');
      }
      return response.json();
    },
  });
}

// ============================================================================
// ADMIN HOOKS
// ============================================================================

/**
 * Hook để fetch analytics data (admin only)
 */
export function useAnalytics() {
  return useQuery({
    queryKey: queryKeys.admin.analytics(),
    queryFn: async () => {
      const response = await fetch('/api/admin/analytics');
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      return response.json();
    },
    staleTime: 2 * 60 * 1000, // 2 phút
  });
}

// ============================================================================
// UTILITY HOOKS
// ============================================================================

/**
 * Hook để invalidate specific query keys
 */
export function useInvalidateQueries() {
  const queryClient = useQueryClient();
  
  return {
    invalidateCoupons: () => queryClient.invalidateQueries({ queryKey: queryKeys.coupons.all() }),
    invalidateProducts: () => queryClient.invalidateQueries({ queryKey: queryKeys.products.all() }),
    invalidateCampaigns: () => queryClient.invalidateQueries({ queryKey: queryKeys.campaigns.all() }),
    invalidateAuth: () => queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() }),
    invalidateAll: () => queryClient.invalidateQueries(),
  };
}

/**
 * Hook để prefetch data với error handling
 */
export function usePrefetch() {
  const queryClient = useQueryClient();

  return {
    prefetchCoupons: async (filters?: Record<string, any>) => {
      try {
        await queryClient.prefetchQuery(queryOptions.coupons.list(filters || {}));
      } catch (error) {
        console.warn('Failed to prefetch coupons:', error);
      }
    },
    prefetchTopProducts: async () => {
      try {
        await queryClient.prefetchQuery({
          queryKey: queryKeys.products.topSelling(),
          queryFn: async () => {
            // Use Hono RPC client instead of apiRequest
            const { api } = await import('../api/hono-client');
            return await api.products.getTopSelling();
          },
        });
      } catch (error) {
        console.warn('Failed to prefetch top products:', error);
      }
    },
  };
}

// ============================================================================
// ERROR RECOVERY HOOKS
// ============================================================================

/**
 * Hook để retry failed queries với UI controls
 */
export function useQueryRetry() {
  const { retryAll, retryQuery } = useRetryFailedQueries();
  const { clearQueryErrors } = useClearErrors();

  return {
    retryAll,
    retryQuery,
    clearErrors: clearQueryErrors,
    retryAuth: () => retryQuery(queryKeys.auth.user()),
    retryCoupons: () => retryQuery(queryKeys.coupons.all()),
    retryProducts: () => retryQuery(queryKeys.products.all()),
  };
}

/**
 * Hook để monitor query states và errors
 */
export function useQueryMonitor() {
  const queryClient = useQueryClient();

  const getFailedQueries = () => {
    return queryClient.getQueryCache().getAll().filter(query =>
      query.state.status === 'error'
    );
  };

  const getLoadingQueries = () => {
    return queryClient.getQueryCache().getAll().filter(query =>
      query.state.status === 'pending'
    );
  };

  const hasErrors = () => getFailedQueries().length > 0;
  const isLoading = () => getLoadingQueries().length > 0;

  return {
    failedQueries: getFailedQueries(),
    loadingQueries: getLoadingQueries(),
    hasErrors: hasErrors(),
    isLoading: isLoading(),
    errorCount: getFailedQueries().length,
    loadingCount: getLoadingQueries().length,
  };
}

/**
 * Hook để handle offline/online states
 */
export function useNetworkStatus() {
  const queryClient = useQueryClient();
  const { retryAll } = useRetryFailedQueries();

  const handleOnline = () => {
    console.log('Network back online, retrying failed queries...');
    retryAll();
  };

  const handleOffline = () => {
    console.log('Network offline detected');
  };

  // Setup event listeners
  React.useEffect(() => {
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []); // Empty deps array vì handleOnline/handleOffline stable

  return {
    isOnline: navigator.onLine,
    isOffline: !navigator.onLine,
  };
}

/**
 * Middleware exports for Hono API
 * 
 * This file exports all middleware functions for easy import and use
 * throughout the Hono application.
 */

// Error handling middleware
export {
  ValidationError,
  DatabaseError,
  AccessTradeError,
  AuthenticationError,
  AuthorizationError,
  RateLimitError,
  formatZodError,
  errorHandler,
  notFoundHandler,
  asyncHandler,
  type ErrorResponse,
} from './error-handler'

// Validation middleware
export {
  createValidator,
  validateJson,
  validateForm,
  validateQuery,
  validateParam,
  validateHeader,
  validateContentType,
  validateRequestSize,
  customValidation,
  sanitizeInput,
  validateRateLimit,
} from './validation'

// Security middleware
export {
  jwtAuth,
  requireRole,
  requireAdmin,
  apiKeyAuth,
  ipWhitelist,
  csrfProtection,
  requestTimeout,
  validateUserAgent,
  validateOrigin,
  securityHeaders,
  securityLogger,
} from './security'

// Database middleware
export {
  createDb,
  databaseMiddleware,
  dbHealthMiddleware,
  dbMetricsMiddleware,
  dbTransactionMiddleware,
  dbTimeoutMiddleware,
  dbPoolMiddleware,
  dbMigrationMiddleware,
  dbBackupMiddleware,
  checkDbHealth,
  runMigrations,
  type DatabaseHealth,
  type DatabaseMetrics,
} from './database'

// Cloudflare Workers bindings middleware
export {
  cloudflareBindingsMiddleware,
  kvMiddleware,
  r2Middleware,
  envMiddleware as cfEnvMiddleware,
  cacheMiddleware,
  analyticsMiddleware,
  type KVOperations,
  type KVPutOptions,
  type KVListOptions,
  type KVListResult,
  type R2Operations,
  type R2PutOptions,
  type R2HTTPMetadata,
  type R2ListOptions,
  type R2Object,
  type R2Objects,
  type R2Range,
} from './cloudflare'

/**
 * Middleware composition utilities
 */

import { Context, Next } from 'hono'

/**
 * Compose multiple middleware functions
 */
export function composeMiddleware(...middlewares: Array<(c: Context, next: Next) => Promise<void>>) {
  return async (c: Context, next: Next) => {
    let index = 0

    async function dispatch(): Promise<void> {
      if (index >= middlewares.length) {
        await next()
        return
      }

      const middleware = middlewares[index++]
      await middleware(c, dispatch)
    }

    await dispatch()
  }
}

/**
 * Conditional middleware wrapper
 */
export function conditionalMiddleware(
  condition: (c: Context) => boolean,
  middleware: (c: Context, next: Next) => Promise<void>
) {
  return async (c: Context, next: Next) => {
    if (condition(c)) {
      await middleware(c, next)
    } else {
      await next()
    }
  }
}

/**
 * Path-based middleware wrapper
 */
export function pathMiddleware(
  paths: string | string[],
  middleware: (c: Context, next: Next) => Promise<void>
) {
  const pathArray = Array.isArray(paths) ? paths : [paths]
  
  return conditionalMiddleware(
    (c) => pathArray.some(path => c.req.path.startsWith(path)),
    middleware
  )
}

/**
 * Method-based middleware wrapper
 */
export function methodMiddleware(
  methods: string | string[],
  middleware: (c: Context, next: Next) => Promise<void>
) {
  const methodArray = Array.isArray(methods) ? methods : [methods]
  
  return conditionalMiddleware(
    (c) => methodArray.includes(c.req.method.toUpperCase()),
    middleware
  )
}

/**
 * Environment-based middleware wrapper
 */
export function envMiddleware(
  env: string | string[],
  middleware: (c: Context, next: Next) => Promise<void>
) {
  const envArray = Array.isArray(env) ? env : [env]
  
  return conditionalMiddleware(
    (c) => envArray.includes(c.env?.NODE_ENV || 'development'),
    middleware
  )
}

/**
 * Predefined middleware stacks
 */

/**
 * Basic security middleware stack
 */
export function basicSecurityStack() {
  return composeMiddleware(
    securityHeaders(),
    securityLogger(),
    requestTimeout(30000)
  )
}

/**
 * API authentication middleware stack
 */
export function apiAuthStack(options?: {
  skipPaths?: string[]
  requireAdmin?: boolean
}) {
  const { skipPaths = [], requireAdmin = false } = options || {}
  
  const middlewares = [
    jwtAuth({ skipPaths }),
    securityLogger(),
  ]
  
  if (requireAdmin) {
    middlewares.push(requireAdmin())
  }
  
  return composeMiddleware(...middlewares)
}

/**
 * Database middleware stack
 */
export function databaseStack(options?: {
  enableMetrics?: boolean
  enableHealth?: boolean
  enableTimeout?: boolean
  timeoutMs?: number
}) {
  const { 
    enableMetrics = true, 
    enableHealth = true, 
    enableTimeout = true,
    timeoutMs = 10000
  } = options || {}
  
  const middlewares = [databaseMiddleware()]
  
  if (enableHealth) {
    middlewares.push(dbHealthMiddleware())
  }
  
  if (enableMetrics) {
    middlewares.push(dbMetricsMiddleware())
  }
  
  if (enableTimeout) {
    middlewares.push(dbTimeoutMiddleware(timeoutMs))
  }
  
  return composeMiddleware(...middlewares)
}

/**
 * Validation middleware stack
 */
export function validationStack(options?: {
  enableSanitization?: boolean
  enableRateLimit?: boolean
  maxRequests?: number
  windowMs?: number
}) {
  const { 
    enableSanitization = true,
    enableRateLimit = false,
    maxRequests = 100,
    windowMs = 60000
  } = options || {}
  
  const middlewares = []
  
  if (enableSanitization) {
    middlewares.push(sanitizeInput())
  }
  
  if (enableRateLimit) {
    middlewares.push(validateRateLimit(maxRequests, windowMs))
  }
  
  return composeMiddleware(...middlewares)
}

/**
 * Complete API middleware stack
 */
export function completeApiStack(options?: {
  skipAuthPaths?: string[]
  requireAdmin?: boolean
  enableDatabase?: boolean
  enableMetrics?: boolean
  enableRateLimit?: boolean
  maxRequests?: number
  windowMs?: number
}) {
  const {
    skipAuthPaths = ['/health', '/api/test'],
    requireAdmin = false,
    enableDatabase = true,
    enableMetrics = true,
    enableRateLimit = true,
    maxRequests = 100,
    windowMs = 60000
  } = options || {}
  
  const middlewares = [
    basicSecurityStack(),
    validationStack({ enableRateLimit, maxRequests, windowMs }),
  ]
  
  if (enableDatabase) {
    middlewares.push(databaseStack({ enableMetrics }))
  }
  
  middlewares.push(
    apiAuthStack({ skipPaths: skipAuthPaths, requireAdmin })
  )
  
  return composeMiddleware(...middlewares)
}

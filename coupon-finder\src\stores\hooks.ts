import { useCallback } from 'react'
import {
  useAuthStore,
  useProductsStore,
  useCouponsStore,
  useUIStore,
  type Product,
  type Coupon,
  type ProductFilters,
  type CouponFilters,
} from './index'

// Combined hooks cho complex operations

/**
 * Hook để handle search operations across products và coupons
 */
export function useSearch() {
  const searchProducts = useProductsStore((state) => state.searchProducts)
  const searchCoupons = useCouponsStore((state) => state.searchCoupons)
  const setGlobalLoading = useUIStore((state) => state.setGlobalLoading)
  const addNotification = useUIStore((state) => state.addNotification)

  const searchAll = useCallback(async (query: string) => {
    if (!query.trim()) return

    setGlobalLoading(true)
    
    try {
      await Promise.all([
        searchProducts(query),
        searchCoupons(query)
      ])
      
      addNotification({
        type: 'success',
        title: 'Tìm kiếm thành công',
        message: `<PERSON><PERSON> tìm thấy kết quả cho "${query}"`,
        duration: 3000
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Lỗi tìm kiếm',
        message: 'Không thể thực hiện tìm kiếm. Vui lòng thử lại.',
        duration: 5000
      })
    } finally {
      setGlobalLoading(false)
    }
  }, [searchProducts, searchCoupons, setGlobalLoading, addNotification])

  return { searchAll }
}

/**
 * Hook để handle product comparison operations
 */
export function useProductComparison() {
  const comparisonProducts = useProductsStore((state) => state.comparisonProducts)
  const addToComparison = useProductsStore((state) => state.addToComparison)
  const removeFromComparison = useProductsStore((state) => state.removeFromComparison)
  const clearComparison = useProductsStore((state) => state.clearComparison)
  const canAddToComparison = useProductsStore((state) => state.canAddToComparison())
  const addNotification = useUIStore((state) => state.addNotification)
  const setComparisonPanelOpen = useUIStore((state) => state.setComparisonPanelOpen)

  const addProductToComparison = useCallback((product: Product) => {
    if (!canAddToComparison) {
      addNotification({
        type: 'warning',
        title: 'Không thể thêm',
        message: 'Chỉ có thể so sánh tối đa 4 sản phẩm',
        duration: 3000
      })
      return
    }

    addToComparison(product)
    addNotification({
      type: 'success',
      title: 'Đã thêm vào so sánh',
      message: `${product.title} đã được thêm vào danh sách so sánh`,
      duration: 3000
    })
    
    // Auto open comparison panel
    setComparisonPanelOpen(true)
  }, [canAddToComparison, addToComparison, addNotification, setComparisonPanelOpen])

  const removeProductFromComparison = useCallback((productId: string) => {
    removeFromComparison(productId)
    addNotification({
      type: 'info',
      title: 'Đã xóa khỏi so sánh',
      message: 'Sản phẩm đã được xóa khỏi danh sách so sánh',
      duration: 3000
    })
  }, [removeFromComparison, addNotification])

  const clearAllComparison = useCallback(() => {
    clearComparison()
    setComparisonPanelOpen(false)
    addNotification({
      type: 'info',
      title: 'Đã xóa tất cả',
      message: 'Danh sách so sánh đã được xóa',
      duration: 3000
    })
  }, [clearComparison, setComparisonPanelOpen, addNotification])

  return {
    comparisonProducts,
    addProductToComparison,
    removeProductFromComparison,
    clearAllComparison,
    canAddToComparison,
    comparisonCount: comparisonProducts.length
  }
}

/**
 * Hook để handle coupon operations
 */
export function useCouponOperations() {
  const addToRecentlyUsed = useCouponsStore((state) => state.addToRecentlyUsed)
  const addNotification = useUIStore((state) => state.addNotification)

  const copyCouponCode = useCallback(async (coupon: Coupon) => {
    try {
      await navigator.clipboard.writeText(coupon.code)
      addToRecentlyUsed(coupon.id)
      addNotification({
        type: 'success',
        title: 'Đã sao chép mã',
        message: `Mã "${coupon.code}" đã được sao chép vào clipboard`,
        duration: 3000
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Lỗi sao chép',
        message: 'Không thể sao chép mã. Vui lòng thử lại.',
        duration: 5000
      })
    }
  }, [addToRecentlyUsed, addNotification])

  const openAffiliateLink = useCallback((coupon: Coupon) => {
    addToRecentlyUsed(coupon.id)
    window.open(coupon.affiliateLink, '_blank', 'noopener,noreferrer')
    
    addNotification({
      type: 'info',
      title: 'Đã mở liên kết',
      message: 'Liên kết affiliate đã được mở trong tab mới',
      duration: 3000
    })
  }, [addToRecentlyUsed, addNotification])

  return {
    copyCouponCode,
    openAffiliateLink
  }
}

// ❌ REMOVED: useAuthOperations - Đã được thay thế bởi TanStack Query auth hooks
// Sử dụng useLogin(), useLogout(), useCurrentUser() từ @/lib/query-hooks thay thế

/**
 * Hook để handle favorites operations
 */
export function useFavorites() {
  const favorites = useProductsStore((state) => state.favorites)
  const toggleFavorite = useProductsStore((state) => state.toggleFavorite)
  const addNotification = useUIStore((state) => state.addNotification)

  const handleToggleFavorite = useCallback((product: Product) => {
    const isFavorite = favorites.includes(product.id)
    toggleFavorite(product.id)
    
    addNotification({
      type: isFavorite ? 'info' : 'success',
      title: isFavorite ? 'Đã xóa khỏi yêu thích' : 'Đã thêm vào yêu thích',
      message: `${product.title} ${isFavorite ? 'đã được xóa khỏi' : 'đã được thêm vào'} danh sách yêu thích`,
      duration: 3000
    })
  }, [favorites, toggleFavorite, addNotification])

  const isFavorite = useCallback((productId: string) => {
    return favorites.includes(productId)
  }, [favorites])

  return {
    favorites,
    handleToggleFavorite,
    isFavorite,
    favoriteCount: favorites.length
  }
}

/**
 * Hook để handle filters operations
 */
export function useFilters() {
  const productFilters = useProductsStore((state) => state.filters)
  const couponFilters = useCouponsStore((state) => state.filters)
  const setProductFilters = useProductsStore((state) => state.setFilters)
  const setCouponFilters = useCouponsStore((state) => state.setFilters)
  const clearProductFilters = useProductsStore((state) => state.clearFilters)
  const clearCouponFilters = useCouponsStore((state) => state.clearFilters)

  const updateProductFilters = useCallback((filters: Partial<ProductFilters>) => {
    setProductFilters(filters)
  }, [setProductFilters])

  const updateCouponFilters = useCallback((filters: Partial<CouponFilters>) => {
    setCouponFilters(filters)
  }, [setCouponFilters])

  const clearAllFilters = useCallback(() => {
    clearProductFilters()
    clearCouponFilters()
  }, [clearProductFilters, clearCouponFilters])

  return {
    productFilters,
    couponFilters,
    updateProductFilters,
    updateCouponFilters,
    clearAllFilters,
    hasProductFilters: Object.keys(productFilters).length > 0,
    hasCouponFilters: Object.keys(couponFilters).length > 0
  }
}

import { createFileRoute } from '@tanstack/react-router';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Copy, ExternalLink, Tag, Clock } from 'lucide-react';
import { CouponSearchForm } from '@/components/forms/coupon-search-form';
import type { CouponSearchFormData } from '@/lib/validation-schemas';
import type { FormSubmissionResult } from '@/lib/form-utils';

export const Route = createFileRoute('/coupons')({
  component: CouponsPage,
});

function CouponsPage() {
  // Handle coupon search submission
  const handleCouponSearch = async (data: CouponSearchFormData): Promise<FormSubmissionResult<CouponSearchFormData>> => {
    try {
      console.log('Searching for coupons with data:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock success response
      return {
        success: true,
        data,
        message: `Tìm thấy ${mockCoupons.length} mã giảm giá cho "${data.searchValue}"`
      };
    } catch (error) {
      return {
        success: false,
        message: 'Có lỗi xảy ra khi tìm kiếm mã giảm giá'
      };
    }
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header Section */}
      <div className='text-center mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 mb-4'>
          🎫 Mã Giảm Giá Shopee
        </h1>
        <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
          Tìm kiếm và sử dụng các mã giảm giá Shopee mới nhất để tiết kiệm tối
          đa khi mua sắm
        </p>
      </div>

      {/* Search Section */}
      <div className='max-w-2xl mx-auto mb-8'>
        <CouponSearchForm
          onSubmit={handleCouponSearch}
          defaultValues={{
            searchType: 'keyword',
            sortBy: 'discount'
          }}
        />
      </div>

      {/* Category Tabs */}
      <div className='flex flex-wrap gap-2 justify-center mb-8'>
        {[
          'Tất cả',
          'Thời trang',
          'Điện tử',
          'Gia dụng',
          'Làm đẹp',
          'Thể thao',
          'Sách',
        ].map(category => (
          <Badge
            key={category}
            variant={category === 'Tất cả' ? 'default' : 'secondary'}
            className='px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors'
          >
            {category}
          </Badge>
        ))}
      </div>

      {/* Coupons Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
        {mockCoupons.map(coupon => (
          <CouponCard key={coupon.id} coupon={coupon} />
        ))}
      </div>

      {/* Load More Button */}
      <div className='text-center mt-8'>
        <Button variant='outline' size='lg'>
          Xem thêm mã giảm giá
        </Button>
      </div>
    </div>
  );
}

function CouponCard({ coupon }: { coupon: any }) {
  const handleCopyCode = () => {
    navigator.clipboard.writeText(coupon.code);
    // TODO: Add toast notification
  };

  return (
    <Card className='hover:shadow-lg transition-shadow duration-200'>
      <CardHeader className='pb-3'>
        <div className='flex items-start justify-between'>
          <div className='flex-1'>
            <CardTitle className='text-lg font-semibold text-gray-900 line-clamp-2'>
              {coupon.title}
            </CardTitle>
            <CardDescription className='mt-1 text-sm text-gray-600'>
              {coupon.store}
            </CardDescription>
          </div>
          <Badge variant='destructive' className='ml-2'>
            -{coupon.discount}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Coupon Code */}
          <div className='flex items-center gap-2 p-3 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200'>
            <Tag className='h-4 w-4 text-gray-500' />
            <code className='flex-1 font-mono font-semibold text-primary'>
              {coupon.code}
            </code>
            <Button
              size='sm'
              variant='ghost'
              onClick={handleCopyCode}
              className='h-8 w-8 p-0'
            >
              <Copy className='h-4 w-4' />
            </Button>
          </div>

          {/* Description */}
          <p className='text-sm text-gray-600 line-clamp-2'>
            {coupon.description}
          </p>

          {/* Expiry Date */}
          <div className='flex items-center gap-2 text-sm text-gray-500'>
            <Clock className='h-4 w-4' />
            <span>Hết hạn: {coupon.expiryDate}</span>
          </div>

          {/* Action Buttons */}
          <div className='flex gap-2 pt-2'>
            <Button className='flex-1' onClick={handleCopyCode}>
              <Copy className='h-4 w-4 mr-2' />
              Sao chép mã
            </Button>
            <Button variant='outline' size='sm' className='px-3'>
              <ExternalLink className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Mock data for development
const mockCoupons = [
  {
    id: 1,
    title: 'Giảm 50K cho đơn hàng từ 299K',
    store: 'Shopee Mall',
    discount: '50K',
    code: 'SAVE50K',
    description:
      'Áp dụng cho tất cả sản phẩm trong Shopee Mall. Không áp dụng cho sản phẩm đã giảm giá.',
    expiryDate: '31/12/2024',
  },
  {
    id: 2,
    title: 'Freeship cho đơn hàng từ 0đ',
    store: 'Shopee Express',
    discount: 'Freeship',
    code: 'FREESHIP0D',
    description: 'Miễn phí vận chuyển cho tất cả đơn hàng. Áp dụng toàn quốc.',
    expiryDate: '30/12/2024',
  },
  {
    id: 3,
    title: 'Giảm 20% tối đa 100K',
    store: 'Thời trang nam',
    discount: '20%',
    code: 'FASHION20',
    description:
      'Giảm giá 20% cho tất cả sản phẩm thời trang nam. Tối đa 100K.',
    expiryDate: '28/12/2024',
  },
  {
    id: 4,
    title: 'Giảm 30K cho đơn hàng từ 199K',
    store: 'Điện tử - Gia dụng',
    discount: '30K',
    code: 'TECH30K',
    description:
      'Áp dụng cho sản phẩm điện tử và gia dụng. Không áp dụng cho Apple.',
    expiryDate: '29/12/2024',
  },
  {
    id: 5,
    title: 'Giảm 15% cho lần đầu mua',
    store: 'Shopee',
    discount: '15%',
    code: 'NEWUSER15',
    description:
      'Dành cho khách hàng mới. Giảm 15% tối đa 50K cho đơn hàng đầu tiên.',
    expiryDate: '31/12/2024',
  },
  {
    id: 6,
    title: 'Combo giảm 40K + Freeship',
    store: 'Shopee Mall',
    discount: '40K + FS',
    code: 'COMBO40K',
    description: 'Giảm 40K và miễn phí vận chuyển cho đơn hàng từ 250K.',
    expiryDate: '27/12/2024',
  },
];

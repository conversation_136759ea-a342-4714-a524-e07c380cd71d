import * as React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ExternalLink, Copy, Check, Info } from 'lucide-react';
import { api } from '@/api/hono-client';

interface CouponCardProps {
  id?: string;
  title: string;
  description: string;
  discount: string;
  code: string;
  expiryDate: string;
  store: string;
  category: string;
  affiliateLink?: string;
  imageUrl?: string;
  isExpired?: boolean;
  className?: string;
  onCouponClick?: (couponId: string) => void;
  onDetailClick?: (couponId: string) => void;
}

export function CouponCard({
  id,
  title,
  description,
  discount,
  code,
  expiryDate,
  store,
  category,
  affiliateLink,
  imageUrl,
  isExpired = false,
  className,
  onCouponClick,
  onDetailClick,
}: CouponCardProps) {
  const [copied, setCopied] = React.useState(false);
  const [isTracking, setIsTracking] = React.useState(false);

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);

      // Track copy action
      if (id) {
        trackCouponAction('copy');
      }
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const handleAffiliateClick = async () => {
    if (!affiliateLink || isExpired) return;

    // Track click action
    if (id) {
      await trackCouponAction('click');
    }

    // Open affiliate link
    window.open(affiliateLink, '_blank', 'noopener,noreferrer');

    // Trigger callback
    if (onCouponClick && id) {
      onCouponClick(id);
    }
  };

  const trackCouponAction = async (action: 'copy' | 'click') => {
    if (!id || isTracking) return;

    setIsTracking(true);
    try {
      await api.coupons.track(id, { action });
    } catch (error) {
      console.error('Failed to track coupon action:', error);
    } finally {
      setIsTracking(false);
    }
  };

  const handleDetailClick = () => {
    if (id && onDetailClick) {
      onDetailClick(id);
    }
  };

  return (
    <Card
      className={cn(
        'relative overflow-hidden transition-all duration-300 hover:shadow-strong hover:-translate-y-1 animate-fade-in',
        isExpired && 'opacity-60 grayscale',
        className
      )}
    >
      {/* Discount Badge */}
      <div className='absolute top-4 right-4 z-10'>
        <Badge variant='success' className='text-sm font-bold shadow-medium'>
          {discount}
        </Badge>
      </div>

      <CardHeader className='pb-3'>
        <div className='flex items-start justify-between gap-3'>
          <div className='flex-1'>
            <CardTitle className='text-lg font-display line-clamp-2'>
              {title}
            </CardTitle>
            <CardDescription className='mt-1 text-sm'>{store}</CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className='space-y-4'>
        <p className='text-sm text-muted-foreground line-clamp-2'>
          {description}
        </p>

        {/* Coupon Code */}
        <div className='flex items-center gap-3 p-4 bg-gradient-secondary rounded-xl border border-border/50'>
          <code className='flex-1 text-sm font-mono font-bold text-primary'>{code}</code>
          <div className='flex gap-2 shrink-0'>
            <Button
              size='sm'
              variant='outline'
              onClick={handleCopyCode}
              disabled={isExpired}
              className='shrink-0'
            >
              {copied ? (
                <>
                  <Check className='h-3 w-3 mr-1' />
                  Đã sao chép!
                </>
              ) : (
                <>
                  <Copy className='h-3 w-3 mr-1' />
                  Sao chép
                </>
              )}
            </Button>
            {affiliateLink && (
              <Button
                size='sm'
                variant='gradient'
                onClick={handleAffiliateClick}
                disabled={isExpired || isTracking}
                className='shrink-0'
              >
                <ExternalLink className='h-3 w-3 mr-1' />
                Dùng ngay
              </Button>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className='flex gap-2'>
          {id && onDetailClick && (
            <Button
              variant='outline'
              size='sm'
              onClick={handleDetailClick}
              className='flex-1'
            >
              <Info className='h-3 w-3 mr-1' />
              Chi tiết
            </Button>
          )}
        </div>

        {/* Footer */}
        <div className='flex items-center justify-between text-xs text-muted-foreground'>
          <Badge variant='outline' className='text-xs'>
            {category}
          </Badge>
          <span className={cn(isExpired && 'text-destructive font-medium')}>
            {isExpired ? 'Đã hết hạn' : `Hết hạn: ${expiryDate}`}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}

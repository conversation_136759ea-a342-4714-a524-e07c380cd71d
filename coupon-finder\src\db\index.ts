import { drizzle } from 'drizzle-orm/d1';
import { drizzle as drizzleLibsql } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from './schema';

// Type for Cloudflare environment
export interface Env {
  DB: D1Database;
}

// Database connection cache for performance optimization
const dbCache = new Map<string, ReturnType<typeof drizzle>>();

// Initialize Drizzle with D1 database with caching
export function createDb(database: D1Database, cacheKey?: string) {
  // Use caching for better performance in Workers
  if (cacheKey && dbCache.has(cacheKey)) {
    return dbCache.get(cacheKey)!;
  }

  const db = drizzle(database, {
    schema,
    logger: process.env.NODE_ENV === 'development'
  });

  if (cacheKey) {
    dbCache.set(cacheKey, db);
  }

  return db;
}

// Get database instance for local development
export function getDb() {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('getDb() should not be used in production. Use createDb() with D1 database instead.');
  }

  // Create local SQLite client
  const client = createClient({
    url: 'file:./dev.db'
  });

  return drizzleLibsql(client, { schema });
}

// Database health check utility
export async function checkDbHealth(database: D1Database): Promise<{
  healthy: boolean;
  latency?: number;
  error?: string;
}> {
  try {
    const startTime = Date.now();

    // Simple health check query
    await database.prepare('SELECT 1').first();

    const latency = Date.now() - startTime;

    return {
      healthy: true,
      latency
    };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown database error'
    };
  }
}

// Database transaction helper
export async function withTransaction<T>(
  database: D1Database,
  callback: (db: ReturnType<typeof createDb>) => Promise<T>
): Promise<T> {
  const db = createDb(database);

  try {
    // D1 doesn't support explicit transactions yet, but we can simulate
    // by using batch operations for multiple queries
    return await callback(db);
  } catch (error) {
    // Log error for debugging
    console.error('Database transaction error:', error);
    throw error;
  }
}

// Database migration helper
export async function runMigrations(database: D1Database): Promise<void> {
  try {
    // Check if migrations table exists
    const migrationCheck = await database.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='__drizzle_migrations'
    `).first();

    if (!migrationCheck) {
      console.log('Migrations table not found. Database may need initialization.');
    }

    console.log('Database migration check completed');
  } catch (error) {
    console.error('Migration check failed:', error);
    throw error;
  }
}

// Export schema and types for use in other files
export * from './schema';
export type DbType = ReturnType<typeof createDb>;

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

// Types cho Products Store
export interface Product {
  id: string
  title: string
  price: number
  originalPrice?: number
  discount?: number
  rating: number
  sold: number
  imageUrl: string
  affiliateLink: string
  merchant: string
  category: string
  description?: string
  features?: string[]
  createdAt: string
}

export interface ProductFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  minRating?: number
  minDiscount?: number
  merchant?: string
  sortBy?: 'price' | 'rating' | 'discount' | 'sold' | 'newest'
  sortOrder?: 'asc' | 'desc'
}

// Products Store chỉ handle CLIENT STATE - Server state được handle bởi TanStack Query
export interface ProductsClientState {
  // Client-only state
  comparisonProducts: Product[]
  favorites: string[] // product IDs
  searchQuery: string
  filters: ProductFilters

  // UI state
  viewMode: 'grid' | 'list'
  showComparisonPanel: boolean
  selectedCategory: string

  // Client actions
  addToComparison: (product: Product) => void
  removeFromComparison: (productId: string) => void
  clearComparison: () => void
  toggleFavorite: (productId: string) => void
  setSearchQuery: (query: string) => void
  setFilters: (filters: Partial<ProductFilters>) => void
  clearFilters: () => void

  // UI actions
  setViewMode: (mode: 'grid' | 'list') => void
  toggleComparisonPanel: () => void
  setSelectedCategory: (category: string) => void

  // Computed
  getComparisonCount: () => number
  canAddToComparison: () => boolean
  isFavorite: (productId: string) => boolean
  isInComparison: (productId: string) => boolean
}

// Products Store - CHỈ CLIENT STATE
export const useProductsStore = create<ProductsClientState>()(
  persist(
    (set, get) => ({
      // Initial state
      comparisonProducts: [],
      favorites: [],
      searchQuery: '',
      filters: {},
      viewMode: 'grid',
      showComparisonPanel: false,
      selectedCategory: 'all',

      // Client actions
      addToComparison: (product: Product) => {
        const { comparisonProducts } = get()
        if (comparisonProducts.length < 4 && !comparisonProducts.find(p => p.id === product.id)) {
          set({
            comparisonProducts: [...comparisonProducts, product]
          })
        }
      },

      removeFromComparison: (productId: string) => {
        const { comparisonProducts } = get()
        set({
          comparisonProducts: comparisonProducts.filter(p => p.id !== productId)
        })
      },

      clearComparison: () => {
        set({ comparisonProducts: [] })
      },

      toggleFavorite: (productId: string) => {
        const { favorites } = get()
        const isFavorite = favorites.includes(productId)

        set({
          favorites: isFavorite
            ? favorites.filter(id => id !== productId)
            : [...favorites, productId]
        })
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      setFilters: (newFilters: Partial<ProductFilters>) => {
        const { filters } = get()
        set({
          filters: { ...filters, ...newFilters }
        })
      },

      clearFilters: () => {
        set({ filters: {} })
      },

      // UI actions
      setViewMode: (mode: 'grid' | 'list') => {
        set({ viewMode: mode })
      },

      toggleComparisonPanel: () => {
        set((state) => ({ showComparisonPanel: !state.showComparisonPanel }))
      },

      setSelectedCategory: (category: string) => {
        set({ selectedCategory: category })
      },

      // Computed properties
      getComparisonCount: () => {
        return get().comparisonProducts.length
      },

      canAddToComparison: () => {
        return get().comparisonProducts.length < 4
      },

      isFavorite: (productId: string) => {
        return get().favorites.includes(productId)
      },

      isInComparison: (productId: string) => {
        return get().comparisonProducts.some(p => p.id === productId)
      },
    }),
    {
      name: 'products-client-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        favorites: state.favorites,
        comparisonProducts: state.comparisonProducts,
        filters: state.filters,
        viewMode: state.viewMode,
        selectedCategory: state.selectedCategory,
      }),
    }
  )
)

// Selectors cho performance optimization - CHỈ CLIENT STATE
export const useComparisonProducts = () => useProductsStore((state) => state.comparisonProducts)
export const useProductFavorites = () => useProductsStore((state) => state.favorites)
export const useProductFilters = () => useProductsStore((state) => state.filters)
export const useSearchQuery = () => useProductsStore((state) => state.searchQuery)
export const useViewMode = () => useProductsStore((state) => state.viewMode)
export const useShowComparisonPanel = () => useProductsStore((state) => state.showComparisonPanel)
export const useSelectedCategory = () => useProductsStore((state) => state.selectedCategory)

// Computed selectors
export const useComparisonCount = () => useProductsStore((state) => state.getComparisonCount())
export const useCanAddToComparison = () => useProductsStore((state) => state.canAddToComparison())

// Actions selectors - CHỈ CLIENT ACTIONS
export const useProductsActions = () => useProductsStore((state) => ({
  addToComparison: state.addToComparison,
  removeFromComparison: state.removeFromComparison,
  clearComparison: state.clearComparison,
  toggleFavorite: state.toggleFavorite,
  setSearchQuery: state.setSearchQuery,
  setFilters: state.setFilters,
  clearFilters: state.clearFilters,
  setViewMode: state.setViewMode,
  toggleComparisonPanel: state.toggleComparisonPanel,
  setSelectedCategory: state.setSelectedCategory,
  isFavorite: state.isFavorite,
  isInComparison: state.isInComparison,
}))

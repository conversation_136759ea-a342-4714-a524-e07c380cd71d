/**
 * Zod v4 Validation Schemas cho Coupon Finder Project
 * 
 * Sử dụng Zod v4 features:
 * - Top-level string validators (z.email(), z.url())
 * - Improved error handling với error functions
 * - Template literals và advanced validation
 * - Type-safe schemas cho TanStack Form integration
 */

import { z } from "zod/v4";

// ===== AUTH SCHEMAS =====

/**
 * Login Form Schema
 * Sử dụng z.email() thay vì z.string().email() (Zod v4)
 */
export const loginSchema = z.object({
  email: z.string().email({
    message: "<PERSON><PERSON> lòng nhập email hợp lệ"
  }),
  password: z.string({
    error: (issue) => 
      issue.input === undefined 
        ? "Mật khẩu là bắt buộc" 
        : "Mật khẩu không hợp lệ"
  }).min(6, {
    error: "Mật khẩu phải có ít nhất 6 ký tự"
  }),
  rememberMe: z.boolean().optional().default(false)
});

/**
 * Register Form Schema
 * Sử dụng Zod v4 error handling và password confirmation
 */
export const registerSchema = z.object({
  email: z.string().email({
    message: "Email không hợp lệ"
  }),
  password: z.string()
    .min(8, { error: "Mật khẩu phải có ít nhất 8 ký tự" })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
      error: "Mật khẩu phải chứa ít nhất 1 chữ thường, 1 chữ hoa và 1 số"
    }),
  confirmPassword: z.string(),
  name: z.string()
    .min(2, { error: "Tên phải có ít nhất 2 ký tự" })
    .max(50, { error: "Tên không được quá 50 ký tự" }),
  acceptTerms: z.boolean({
    error: "Bạn phải đồng ý với điều khoản sử dụng"
  }).refine(val => val === true, {
    message: "Bạn phải đồng ý với điều khoản sử dụng"
  })
}).refine(data => data.password === data.confirmPassword, {
  message: "Mật khẩu xác nhận không khớp",
  path: ["confirmPassword"]
});

// ===== COUPON SCHEMAS =====

/**
 * Coupon Search Form Schema
 * Sử dụng z.url() và z.templateLiteral() (Zod v4 features)
 */
export const couponSearchSchema = z.object({
  searchType: z.enum(["url", "keyword"]),
  searchValue: z.string().min(1, "Vui lòng nhập từ khóa hoặc URL"),
  category: z.string().optional(),
  sortBy: z.enum(["discount", "expiry", "popularity"]).default("discount")
});

/**
 * Shopee URL Template Schema (Zod v4 templateLiteral)
 */
export const shopeeUrlSchema = z.templateLiteral([
  "https://shopee.vn/",
  z.string().min(1),
  "-i.",
  z.string().regex(/^\d+$/, "Shop ID phải là số"),
  ".",
  z.string().regex(/^\d+$/, "Product ID phải là số")
]);

// ===== PRODUCT SCHEMAS =====

/**
 * Product Comparison Schema
 * Sử dụng z.array().min() thay vì z.array().nonempty() (Zod v4)
 */
export const productComparisonSchema = z.object({
  productIds: z.array(z.string())
    .min(2, { error: "Cần chọn ít nhất 2 sản phẩm để so sánh" })
    .max(4, { error: "Chỉ có thể so sánh tối đa 4 sản phẩm" }),
  compareFields: z.array(z.enum([
    "price", "discount", "rating", "sold", "shipping", "features"
  ])).min(1, { error: "Cần chọn ít nhất 1 tiêu chí so sánh" }),
  sortBy: z.enum(["price", "discount", "rating"]).default("price")
});

/**
 * Product Filter Schema
 */
export const productFilterSchema = z.object({
  category: z.string().optional(),
  priceRange: z.object({
    min: z.number().min(0, { error: "Giá tối thiểu phải >= 0" }).optional(),
    max: z.number().min(0, { error: "Giá tối đa phải >= 0" }).optional()
  }).refine(data => {
    if (data.min && data.max) {
      return data.min <= data.max;
    }
    return true;
  }, {
    message: "Giá tối thiểu phải nhỏ hơn giá tối đa",
    path: ["min"]
  }).optional(),
  rating: z.number().min(1).max(5).optional(),
  discountRange: z.object({
    min: z.number().min(0).max(100).optional(),
    max: z.number().min(0).max(100).optional()
  }).optional(),
  location: z.string().optional(),
  freeShipping: z.boolean().optional()
});

// ===== ADMIN SCHEMAS =====

/**
 * Admin Product Management Schema
 */
export const adminProductSchema = z.object({
  title: z.string()
    .min(5, { error: "Tiêu đề phải có ít nhất 5 ký tự" })
    .max(200, { error: "Tiêu đề không được quá 200 ký tự" }),
  description: z.string()
    .min(10, { error: "Mô tả phải có ít nhất 10 ký tự" })
    .max(1000, { error: "Mô tả không được quá 1000 ký tự" }),
  price: z.number()
    .min(0, { error: "Giá phải >= 0" })
    .max(999999999, { error: "Giá quá lớn" }),
  originalPrice: z.number().min(0).optional(),
  discount: z.number().min(0).max(100).optional(),
  categoryId: z.string().min(1, { error: "Vui lòng chọn danh mục" }),
  imageUrl: z.string().url({ message: "URL hình ảnh không hợp lệ" }),
  affiliateLink: z.string().url({ message: "Link affiliate không hợp lệ" }),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.string(), z.unknown()).optional()
});

/**
 * Category Management Schema
 */
export const categorySchema = z.object({
  name: z.string()
    .min(2, { error: "Tên danh mục phải có ít nhất 2 ký tự" })
    .max(50, { error: "Tên danh mục không được quá 50 ký tự" }),
  slug: z.string()
    .min(2, { error: "Slug phải có ít nhất 2 ký tự" })
    .regex(/^[a-z0-9-]+$/, { error: "Slug chỉ được chứa chữ thường, số và dấu gạch ngang" }),
  description: z.string().max(500).optional(),
  icon: z.string().optional(),
  parentId: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().min(0).default(0)
});

// ===== CONTACT & FEEDBACK SCHEMAS =====

/**
 * Contact Form Schema
 */
export const contactSchema = z.object({
  name: z.string()
    .min(2, { error: "Tên phải có ít nhất 2 ký tự" })
    .max(100, { error: "Tên không được quá 100 ký tự" }),
  email: z.string().email({ message: "Email không hợp lệ" }),
  subject: z.string()
    .min(5, { error: "Tiêu đề phải có ít nhất 5 ký tự" })
    .max(200, { error: "Tiêu đề không được quá 200 ký tự" }),
  message: z.string()
    .min(10, { error: "Tin nhắn phải có ít nhất 10 ký tự" })
    .max(2000, { error: "Tin nhắn không được quá 2000 ký tự" }),
  type: z.enum(["support", "feedback", "bug", "feature"], {
    error: "Vui lòng chọn loại tin nhắn"
  })
});

// ===== TYPE EXPORTS =====

export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type CouponSearchFormData = z.infer<typeof couponSearchSchema>;
export type ProductComparisonFormData = z.infer<typeof productComparisonSchema>;
export type ProductFilterFormData = z.infer<typeof productFilterSchema>;
export type AdminProductFormData = z.infer<typeof adminProductSchema>;
export type CategoryFormData = z.infer<typeof categorySchema>;
export type ContactFormData = z.infer<typeof contactSchema>;

// ===== VALIDATION UTILITIES =====

/**
 * Utility function để validate data với schema
 */
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: string[];
} {
  try {
    const result = schema.safeParse(data);
    if (result.success) {
      return { success: true, data: result.data };
    } else {
      return {
        success: false,
        errors: result.error.errors.map(err => err.message)
      };
    }
  } catch (error) {
    return {
      success: false,
      errors: ["Validation error occurred"]
    };
  }
}

/**
 * Utility để format Zod errors cho UI display
 */
export function formatZodErrors(error: z.ZodError): Record<string, string> {
  const formattedErrors: Record<string, string> = {};
  
  error.errors.forEach(err => {
    const path = err.path.join('.');
    formattedErrors[path] = err.message;
  });
  
  return formattedErrors;
}

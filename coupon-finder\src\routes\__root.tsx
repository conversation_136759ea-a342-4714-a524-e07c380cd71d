import {
  HeadContent,
  Link,
  Outlet,
  Scripts,
  createRootRoute,
} from '@tanstack/react-router';
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools';
import * as React from 'react';
import { DefaultCatchBoundary } from '@/components/DefaultCatchBoundary';
import { NotFound } from '@/components/NotFound';
import { AppProvider } from '@/lib/app-provider';
import { useAuth } from '@/lib/auth-context';
import appCss from '@/styles/app.css?url';
import { seo } from '@/utils/seo';

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      ...seo({
        title:
          'Coupon Finder | Shopee Coupon Finder & Product Comparison Platform',
        description:
          'Nền tảng tìm kiếm mã giảm gi<PERSON> và so sánh sản phẩm thông minh. Tiết kiệm tối đa khi mua sắm online.',
      }),
    ],
    links: [
      { rel: 'stylesheet', href: appCss },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: '/apple-touch-icon.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: '/favicon-16x16.png',
      },
      { rel: 'manifest', href: '/site.webmanifest', color: '#fffff' },
      { rel: 'icon', href: '/favicon.ico' },
    ],
  }),
  errorComponent: props => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    );
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
});

function RootComponent() {
  return (
    <RootDocument>
      <AppProvider>
        <NavigationWrapper />
        <Outlet />
      </AppProvider>
    </RootDocument>
  );
}

function NavigationWrapper() {
  // Đảm bảo QueryClient đã được setup trước khi render Navigation
  return <Navigation />;
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body className='bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 min-h-screen'>
        {children}
        <TanStackRouterDevtools position='bottom-right' />
        <Scripts />
      </body>
    </html>
  );
}

function Navigation() {
  const { user, isAuthenticated, logout } = useAuth();

  return (
    <nav className='bg-background/80 backdrop-blur-sm shadow-sm'>
      <div className='container mx-auto px-4'>
        <div className='flex items-center justify-between h-16'>
          <Link
            to='/'
            className='text-xl font-display font-bold text-primary'
            activeOptions={{ exact: true }}
          >
            🎫 Coupon Finder
          </Link>
          <div className='flex items-center gap-6'>
            <Link
              to='/'
              activeProps={{
                className: 'font-semibold text-primary',
              }}
              className='text-muted-foreground hover:text-foreground transition-colors'
              activeOptions={{ exact: true }}
            >
              Trang chủ
            </Link>
            <Link
              to='/coupons'
              activeProps={{
                className: 'font-semibold text-primary',
              }}
              className='text-muted-foreground hover:text-foreground transition-colors'
            >
              Mã giảm giá
            </Link>
            <Link
              to='/compare'
              activeProps={{
                className: 'font-semibold text-primary',
              }}
              className='text-muted-foreground hover:text-foreground transition-colors'
            >
              So sánh
            </Link>
            <Link
              to='/deals'
              activeProps={{
                className: 'font-semibold text-primary',
              }}
              className='text-muted-foreground hover:text-foreground transition-colors'
            >
              Top Deals
            </Link>

            {/* Auth Section */}
            {isAuthenticated ? (
              <div className='flex items-center gap-4'>
                {user?.role === 'admin' && (
                  <Link
                    to='/admin'
                    activeProps={{
                      className: 'font-semibold text-primary',
                    }}
                    className='text-muted-foreground hover:text-foreground transition-colors'
                  >
                    Admin
                  </Link>
                )}
                <span className='text-sm text-muted-foreground'>
                  Xin chào, {user?.email}
                </span>
                <button
                  onClick={logout}
                  className='text-sm text-muted-foreground hover:text-foreground transition-colors'
                >
                  Đăng xuất
                </button>
              </div>
            ) : (
              <Link
                to='/login'
                activeProps={{
                  className: 'font-semibold text-primary',
                }}
                className='text-muted-foreground hover:text-foreground transition-colors'
              >
                Đăng nhập
              </Link>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}

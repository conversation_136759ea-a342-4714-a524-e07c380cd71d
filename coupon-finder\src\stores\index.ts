// Export all stores
export * from './auth-store'
export * from './products-store'
export * from './coupons-store'
export * from './ui-store'
export * from './hooks'

// Re-export commonly used types
export type {
  User,
  AuthState,
} from './auth-store'

export type {
  Product,
  ProductFilters,
  ProductsState,
} from './products-store'

export type {
  Coupon,
  CouponFilters,
  CouponsState,
} from './coupons-store'

export type {
  Theme,
  NotificationType,
  Notification,
  Modal,
  UIState,
} from './ui-store'

// Store initialization helper
export const initializeStores = () => {
  // Force light theme always
  useUIStore.getState().setTheme('light')
  document.documentElement.classList.remove('dark')

  // Remove any existing theme listeners to prevent auto dark mode
  // No need to listen for system theme changes since we force light mode

  // Initialize mobile detection
  const checkMobile = () => {
    const isMobile = window.innerWidth < 768
    useUIStore.getState().setIsMobile(isMobile)
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)

  // Auto-fetch initial data
  const fetchInitialData = async () => {
    try {
      // Fetch featured coupons only (products are handled by TanStack Query)
      await useCouponsStore.getState().fetchFeaturedCoupons()
    } catch (error) {
      console.error('Failed to fetch initial data:', error)
    }
  }

  // Delay initial data fetch to avoid blocking app startup
  setTimeout(fetchInitialData, 1000)
}

// Store reset helper (useful for testing)
export const resetAllStores = () => {
  // Clear localStorage
  localStorage.removeItem('auth-storage')
  localStorage.removeItem('products-storage')
  localStorage.removeItem('coupons-storage')
  localStorage.removeItem('ui-storage')
  
  // Reset stores to initial state
  useAuthStore.setState({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  })
  
  useProductsStore.setState({
    products: [],
    featuredProducts: [],
    topSellingProducts: [],
    comparisonProducts: [],
    favorites: [],
    searchQuery: '',
    filters: {},
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalProducts: 0,
  })
  
  useCouponsStore.setState({
    coupons: [],
    featuredCoupons: [],
    categoryCoupons: {},
    searchQuery: '',
    filters: {},
    selectedCategory: 'all',
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalCoupons: 0,
    recentlyUsed: [],
  })
  
  useUIStore.setState({
    theme: 'system',
    globalLoading: false,
    loadingStates: {},
    notifications: [],
    modals: [],
    sidebarOpen: true,
    sidebarCollapsed: false,
    isMobile: false,
    searchOpen: false,
    comparisonPanelOpen: false,
  })
}

// Import stores for initialization
import { useAuthStore } from './auth-store'
import { useProductsStore } from './products-store'
import { useCouponsStore } from './coupons-store'
import { useUIStore } from './ui-store'

/**
 * Test script để kiểm tra các API endpoints c<PERSON>a ứng dụng
 * Chạy: node test-local-api.js
 */

const BASE_URL = 'http://localhost:3000/api';

async function testLocalAPI() {
  console.log('🔍 Testing Local API Endpoints...\n');

  // Test basic API endpoint
  console.log('📡 Testing basic API endpoint: /api/test');
  try {
    const response = await fetch(`${BASE_URL}/test`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Success!`, data);
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Error: ${errorText}`);
    }
  } catch (error) {
    console.log(`   💥 Exception: ${error.message}`);
  }
  console.log('');

  // Test coupons endpoints
  const couponTests = [
    {
      name: 'Get coupons (default)',
      url: `${BASE_URL}/coupons`,
    },
    {
      name: 'Search coupons by merchant (Shopee)',
      url: `${BASE_URL}/coupons?merchant=shopee&limit=5`,
    },
    {
      name: 'Search coupons by URL',
      url: `${BASE_URL}/coupons?url=https://shopee.vn/product/123&limit=3`,
    },
    {
      name: 'Search coupons by keyword',
      url: `${BASE_URL}/coupons?keyword=fashion&limit=5`,
    },
  ];

  for (const test of couponTests) {
    console.log(`📡 Testing: ${test.name}`);
    console.log(`   URL: ${test.url}`);
    
    try {
      const response = await fetch(test.url);
      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ Success! Response type: ${typeof data}`);
        
        if (data.data && Array.isArray(data.data)) {
          console.log(`   📊 Coupons found: ${data.data.length}`);
          if (data.data.length > 0) {
            const firstCoupon = data.data[0];
            console.log(`   🔍 First coupon: ${firstCoupon.name || firstCoupon.title || 'No name'}`);
            console.log(`   🏪 Merchant: ${firstCoupon.merchant || 'Unknown'}`);
          }
        }
        
        if (data.cached) {
          console.log(`   💾 Data from cache`);
        }
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText.substring(0, 200)}...`);
      }
    } catch (error) {
      console.log(`   💥 Exception: ${error.message}`);
    }
    console.log('');
  }

  // Test products endpoints
  const productTests = [
    {
      name: 'Search products (default)',
      url: `${BASE_URL}/products?limit=5`,
    },
    {
      name: 'Search products by keyword',
      url: `${BASE_URL}/products?keyword=laptop&limit=3`,
    },
    {
      name: 'Get top selling products',
      url: `${BASE_URL}/products/top-selling?limit=5`,
    },
  ];

  for (const test of productTests) {
    console.log(`📡 Testing: ${test.name}`);
    console.log(`   URL: ${test.url}`);
    
    try {
      const response = await fetch(test.url);
      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   ✅ Success! Response type: ${typeof data}`);
        
        if (data.data && Array.isArray(data.data)) {
          console.log(`   📊 Products found: ${data.data.length}`);
          if (data.data.length > 0) {
            const firstProduct = data.data[0];
            console.log(`   🔍 First product: ${firstProduct.name || 'No name'}`);
            console.log(`   💰 Price: ${firstProduct.price || 'No price'}`);
          }
        }
      } else {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText.substring(0, 200)}...`);
      }
    } catch (error) {
      console.log(`   💥 Exception: ${error.message}`);
    }
    console.log('');
  }

  // Test campaigns endpoint
  console.log(`📡 Testing: Get campaigns`);
  console.log(`   URL: ${BASE_URL}/campaigns?limit=5`);
  
  try {
    const response = await fetch(`${BASE_URL}/campaigns?limit=5`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Success! Response type: ${typeof data}`);
      
      if (data.data && Array.isArray(data.data)) {
        console.log(`   📊 Campaigns found: ${data.data.length}`);
        if (data.data.length > 0) {
          const firstCampaign = data.data[0];
          console.log(`   🔍 First campaign: ${firstCampaign.name || 'No name'}`);
          console.log(`   🏪 Merchant: ${firstCampaign.merchant || 'Unknown'}`);
        }
      }
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`   💥 Exception: ${error.message}`);
  }
  console.log('');

  console.log('🎉 API Testing completed!');
}

// Chạy test
testLocalAPI().catch(console.error);

@import 'tailwindcss';

:root {
  /* Modern purple-blue theme inspired by the reference design */
  --background: oklch(0.99 0.005 280);
  --foreground: oklch(0.15 0.02 280);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 280);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 280);
  --primary: oklch(0.65 0.25 280);
  --primary-foreground: oklch(0.98 0.01 280);
  --secondary: oklch(0.96 0.01 280);
  --secondary-foreground: oklch(0.25 0.02 280);
  --muted: oklch(0.96 0.01 280);
  --muted-foreground: oklch(0.55 0.02 280);
  --accent: oklch(0.94 0.02 280);
  --accent-foreground: oklch(0.25 0.02 280);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(0.92 0.01 280);
  --input: oklch(0.98 0.005 280);
  --ring: oklch(0.65 0.25 280);
  --chart-1: oklch(0.646 0.222 280);
  --chart-2: oklch(0.6 0.118 240);
  --chart-3: oklch(0.7 0.15 320);
  --chart-4: oklch(0.55 0.2 260);
  --chart-5: oklch(0.75 0.18 300);
  --radius: 0.75rem;
}

.dark {
  /* Dark theme với purple-blue design - match với light theme */
  --background: oklch(0.08 0.02 280);
  --foreground: oklch(0.95 0.01 280);
  --card: oklch(0.12 0.02 280);
  --card-foreground: oklch(0.95 0.01 280);
  --popover: oklch(0.12 0.02 280);
  --popover-foreground: oklch(0.95 0.01 280);
  --primary: oklch(0.70 0.25 280);
  --primary-foreground: oklch(0.08 0.02 280);
  --secondary: oklch(0.18 0.02 280);
  --secondary-foreground: oklch(0.90 0.01 280);
  --muted: oklch(0.18 0.02 280);
  --muted-foreground: oklch(0.65 0.02 280);
  --accent: oklch(0.20 0.02 280);
  --accent-foreground: oklch(0.90 0.01 280);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(0.22 0.02 280);
  --input: oklch(0.15 0.02 280);
  --ring: oklch(0.70 0.25 280);
  --chart-1: oklch(0.646 0.222 280);
  --chart-2: oklch(0.6 0.118 240);
  --chart-3: oklch(0.7 0.15 320);
  --chart-4: oklch(0.55 0.2 260);
  --chart-5: oklch(0.75 0.18 300);
}

@theme {
  /* Custom fonts for Coupon Finder */
  --font-family-sans: 'Inter', 'system-ui', 'sans-serif';
  --font-family-display: 'Satoshi', 'Inter', 'sans-serif';

  /* Custom breakpoints */
  --breakpoint-3xl: 1920px;

  /* Modern purple-blue gradient palette inspired by the reference design */
  --color-primary-50: oklch(0.98 0.01 280);
  --color-primary-100: oklch(0.95 0.03 280);
  --color-primary-200: oklch(0.89 0.08 280);
  --color-primary-300: oklch(0.83 0.15 280);
  --color-primary-400: oklch(0.76 0.20 280);
  --color-primary-500: oklch(0.65 0.25 280);
  --color-primary-600: oklch(0.58 0.22 280);
  --color-primary-700: oklch(0.50 0.18 280);
  --color-primary-800: oklch(0.42 0.15 280);
  --color-primary-900: oklch(0.35 0.12 280);
  --color-primary-950: oklch(0.22 0.08 280);

  /* Success/Deal colors */
  --color-success-50: oklch(0.97 0.04 145);
  --color-success-100: oklch(0.93 0.08 145);
  --color-success-200: oklch(0.85 0.16 145);
  --color-success-300: oklch(0.76 0.24 145);
  --color-success-400: oklch(0.66 0.31 145);
  --color-success-500: oklch(0.57 0.37 145);
  --color-success-600: oklch(0.48 0.31 145);
  --color-success-700: oklch(0.4 0.25 145);
  --color-success-800: oklch(0.33 0.2 145);
  --color-success-900: oklch(0.28 0.16 145);
  --color-success-950: oklch(0.16 0.09 145);

  /* Warning/Discount colors */
  --color-warning-50: oklch(0.98 0.02 85);
  --color-warning-100: oklch(0.95 0.05 85);
  --color-warning-200: oklch(0.89 0.11 85);
  --color-warning-300: oklch(0.82 0.17 85);
  --color-warning-400: oklch(0.74 0.22 85);
  --color-warning-500: oklch(0.67 0.27 85);
  --color-warning-600: oklch(0.59 0.24 85);
  --color-warning-700: oklch(0.5 0.2 85);
  --color-warning-800: oklch(0.42 0.16 85);
  --color-warning-900: oklch(0.35 0.13 85);
  --color-warning-950: oklch(0.21 0.08 85);

  /* Custom easing for smooth animations */
  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);

  /* Custom spacing for coupon cards */
  --spacing-18: 4.5rem;
  --spacing-22: 5.5rem;

  /* Shadcn/ui color mappings for Tailwind CSS v4 */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Border radius mappings */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  html {
    color-scheme: light;
  }

  * {
    @apply border-border;
  }

  html,
  body {
    @apply text-foreground bg-background antialiased;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    /* Force light theme */
    background-color: oklch(0.99 0.005 280) !important;
    color: oklch(0.15 0.02 280) !important;
  }

  /* Force light theme for all elements */
  html:not(.dark),
  html:not(.dark) * {
    color-scheme: light !important;
  }

  /* Prevent any dark backgrounds */
  html.dark,
  html.dark * {
    background-color: white !important;
    color: oklch(0.15 0.02 280) !important;
  }

  .using-mouse * {
    outline: none !important;
  }
}

@layer components {
  /* Modern card with soft shadows - force white background */
  .card-modern {
    background-color: white !important;
    border: 0 !important;
    border-radius: 1rem !important;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.08), 0 1px 2px -1px rgb(0 0 0 / 0.06) !important;
    transition: all 0.3s cubic-bezier(0.3, 0, 0, 1) !important;
    color: oklch(0.15 0.02 280) !important;
  }

  .card-modern:hover {
    box-shadow: 0 10px 25px -5px rgb(0 0 0 / 0.12), 0 8px 10px -6px rgb(0 0 0 / 0.08) !important;
    transform: translateY(-2px) !important;
  }

  /* Force white background for all card elements */
  .card,
  [data-card],
  .bg-card,
  [data-slot="card"] {
    background-color: white !important;
    color: oklch(0.15 0.02 280) !important;
  }

  /* Force correct text colors in cards */
  [data-slot="card"] *,
  [data-slot="card-header"] *,
  [data-slot="card-content"] *,
  [data-slot="card-title"],
  [data-slot="card-description"] {
    color: inherit !important;
  }

  /* Specific overrides for card text elements */
  [data-slot="card"] .text-gray-900,
  [data-slot="card"] .text-gray-600 {
    color: oklch(0.15 0.02 280) !important;
  }

  [data-slot="card-description"] {
    color: oklch(0.55 0.02 280) !important;
  }

  /* TABLE TEXT FIXES: Force dark text for all table content */
  table,
  table *,
  [role="table"],
  [role="table"] *,
  .table,
  .table * {
    color: oklch(0.15 0.02 280) !important;
  }

  /* Table headers should be darker */
  th,
  [role="columnheader"],
  .table-header,
  thead,
  thead * {
    color: oklch(0.10 0.02 280) !important;
    font-weight: 600 !important;
  }

  /* Specific table text classes */
  .text-muted-foreground,
  .text-gray-500,
  .text-gray-400,
  .text-gray-600,
  .text-gray-700,
  .text-gray-800,
  .text-gray-900 {
    color: oklch(0.15 0.02 280) !important;
  }

  /* Table cell content */
  td,
  [role="cell"],
  .table-cell {
    color: oklch(0.15 0.02 280) !important;
  }

  /* Product comparison table specific fixes */
  .font-medium,
  .font-semibold,
  .font-bold {
    color: oklch(0.10 0.02 280) !important;
  }

  /* UNIVERSAL TEXT FIX: All text elements should be dark */
  h1, h2, h3, h4, h5, h6,
  p, span, div, label,
  .text-sm, .text-xs, .text-lg, .text-xl,
  .line-clamp-2, .text-center {
    color: oklch(0.15 0.02 280) !important;
  }

  /* Specific overrides for light text that should be dark */
  .text-muted-foreground,
  .text-secondary-foreground,
  .text-gray-400,
  .text-gray-500,
  .text-gray-600 {
    color: oklch(0.35 0.02 280) !important; /* Slightly lighter but still readable */
  }

  /* Keep red text for prices */
  .text-red-600,
  .text-red-500 {
    color: #dc2626 !important; /* Keep red for prices */
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: linear-gradient(135deg, oklch(0.65 0.25 280) 0%, oklch(0.60 0.22 260) 100%);
    color: white !important;
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, oklch(0.96 0.01 280) 0%, oklch(0.94 0.02 300) 100%);
  }

  /* RESET: Remove all previous button text color rules */

  /* CLEAN APPROACH: Simple and clear button text colors */

  /* CORRECT APPROACH: Only gradient buttons get white text, others get black text */

  /* Default: All buttons and badges have black text */
  button,
  [data-slot="button"],
  [data-slot="badge"],
  span[data-slot="badge"] {
    color: oklch(0.15 0.02 280) !important; /* Dark text */
  }

  /* SPECIFIC FIX: Secondary badges get dark text for better contrast */
  [data-slot="badge"][class*="variant-secondary"],
  span[data-slot="badge"][class*="variant-secondary"],
  .bg-gradient-secondary,
  span.bg-gradient-secondary,
  [data-slot="badge"].bg-gradient-secondary,
  span[data-slot="badge"].bg-gradient-secondary {
    color: oklch(0.15 0.02 280) !important; /* Dark text on light background */
  }

  /* ULTRA HIGH SPECIFICITY: Force dark text for light gray badges */
  span[data-slot="badge"][class*="bg-gradient-secondary"],
  span.inline-flex.items-center.justify-center[data-slot="badge"][class*="bg-gradient-secondary"],
  [data-slot="badge"].inline-flex.items-center.justify-center[class*="bg-gradient-secondary"] {
    color: oklch(0.15 0.02 280) !important; /* Force dark text */
  }

  /* NUCLEAR OPTION: Target exact classes from the image */
  span.inline-flex.items-center.justify-center.rounded-lg.border-0.px-2\.5.py-1.text-xs.font-semibold.w-fit.whitespace-nowrap.shrink-0.gap-1.focus-visible\:border-ring.focus-visible\:ring-ring\/50.focus-visible\:ring-\[3px\].aria-invalid\:ring-destructive\/20.dark\:aria-invalid\:ring-destructive\/40.aria-invalid\:border-destructive.transition-all.duration-300.overflow-hidden.shadow-soft.bg-gradient-secondary.text-secondary-foreground.hover\:shadow-medium.px-4.py-2.cursor-pointer.hover\:bg-primary.hover\:text-primary-foreground.transition-colors {
    color: oklch(0.15 0.02 280) !important; /* Force dark text for secondary badges */
  }

  /* SIMPLER NUCLEAR OPTION: Any element with these specific classes */
  .bg-gradient-secondary.text-secondary-foreground,
  span.bg-gradient-secondary.text-secondary-foreground,
  [data-slot="badge"].bg-gradient-secondary.text-secondary-foreground {
    color: oklch(0.15 0.02 280) !important; /* Override secondary-foreground color */
  }

  /* ONLY gradient buttons and dark background buttons get white text */
  button[class*="variant-default"]:not([class*="variant-outline"]):not([class*="variant-ghost"]):not([class*="variant-secondary"]),
  button[class*="variant-gradient"],
  button.bg-gradient-primary,
  button.btn-gradient,
  button:not([class*="variant-"]):not([class*="bg-background"]):not([class*="border"]),
  [data-slot="button"][class*="variant-default"]:not([class*="variant-outline"]):not([class*="variant-ghost"]):not([class*="variant-secondary"]),
  [data-slot="button"][class*="variant-gradient"],
  [data-slot="button"].bg-gradient-primary,
  [data-slot="button"].btn-gradient,
  [data-slot="button"]:not([class*="variant-"]):not([class*="bg-background"]):not([class*="border"]) {
    color: white !important; /* White text for gradient backgrounds */
  }

  /* Green background buttons AND badges get white text */
  button[class*="bg-green"],
  button[class*="bg-emerald"],
  button.bg-green-500,
  button.bg-green-600,
  button.bg-green-700,
  button.bg-emerald-500,
  button.bg-emerald-600,
  button.bg-emerald-700,
  [data-slot="button"][class*="bg-green"],
  [data-slot="button"][class*="bg-emerald"],
  [data-slot="button"].bg-green-500,
  [data-slot="button"].bg-green-600,
  [data-slot="button"].bg-green-700,
  [data-slot="button"].bg-emerald-500,
  [data-slot="button"].bg-emerald-600,
  [data-slot="button"].bg-emerald-700,
  [data-slot="badge"][class*="bg-green"],
  [data-slot="badge"][class*="bg-emerald"],
  [data-slot="badge"][class*="from-green"],
  [data-slot="badge"][class*="to-emerald"],
  span[data-slot="badge"][class*="bg-green"],
  span[data-slot="badge"][class*="bg-emerald"],
  span[data-slot="badge"][class*="from-green"],
  span[data-slot="badge"][class*="to-emerald"] {
    color: white !important; /* White text for green backgrounds */
  }

  /* Other dark background colors that need white text */
  button[class*="bg-blue-5"],
  button[class*="bg-blue-6"],
  button[class*="bg-blue-7"],
  button[class*="bg-purple-5"],
  button[class*="bg-purple-6"],
  button[class*="bg-purple-7"],
  button[class*="bg-indigo-5"],
  button[class*="bg-indigo-6"],
  button[class*="bg-indigo-7"],
  button[class*="bg-red-5"],
  button[class*="bg-red-6"],
  button[class*="bg-red-7"],
  [data-slot="button"][class*="bg-blue-5"],
  [data-slot="button"][class*="bg-blue-6"],
  [data-slot="button"][class*="bg-blue-7"],
  [data-slot="button"][class*="bg-purple-5"],
  [data-slot="button"][class*="bg-purple-6"],
  [data-slot="button"][class*="bg-purple-7"],
  [data-slot="button"][class*="bg-indigo-5"],
  [data-slot="button"][class*="bg-indigo-6"],
  [data-slot="button"][class*="bg-indigo-7"],
  [data-slot="button"][class*="bg-red-5"],
  [data-slot="button"][class*="bg-red-6"],
  [data-slot="button"][class*="bg-red-7"] {
    color: white !important; /* White text for dark backgrounds */
  }

  /* UNIVERSAL: All badges with DARK gradient backgrounds get white text (EXCEPT secondary) */
  [data-slot="badge"][class*="bg-gradient-primary"],
  [data-slot="badge"][class*="from-green"],
  [data-slot="badge"][class*="from-blue"],
  [data-slot="badge"][class*="from-purple"],
  [data-slot="badge"][class*="from-red"],
  [data-slot="badge"][class*="to-emerald"],
  [data-slot="badge"][class*="to-blue"],
  [data-slot="badge"][class*="to-purple"],
  [data-slot="badge"][class*="to-red"],
  span[data-slot="badge"][class*="bg-gradient-primary"],
  span[data-slot="badge"][class*="from-green"],
  span[data-slot="badge"][class*="from-blue"],
  span[data-slot="badge"][class*="from-purple"],
  span[data-slot="badge"][class*="from-red"],
  span[data-slot="badge"][class*="to-emerald"],
  span[data-slot="badge"][class*="to-blue"],
  span[data-slot="badge"][class*="to-purple"],
  span[data-slot="badge"][class*="to-red"] {
    color: white !important; /* White text for dark gradient badges */
  }

  /* EXCLUDE: Secondary gradient badges keep dark text */
  [data-slot="badge"][class*="bg-gradient-secondary"]:not([class*="from-green"]):not([class*="from-blue"]):not([class*="from-purple"]),
  span[data-slot="badge"][class*="bg-gradient-secondary"]:not([class*="from-green"]):not([class*="from-blue"]):not([class*="from-purple"]) {
    color: oklch(0.15 0.02 280) !important; /* Dark text for light secondary gradient */
  }

  /* FINAL OVERRIDE: Maximum specificity for secondary badges */
  span[data-slot="badge"].bg-gradient-secondary.text-secondary-foreground.hover\:shadow-medium,
  [data-slot="badge"].bg-gradient-secondary.text-secondary-foreground.hover\:shadow-medium {
    color: oklch(0.15 0.02 280) !important; /* Force dark text with maximum specificity */
  }

  /* Red background elements get white text */
  [class*="bg-red-5"],
  [class*="bg-red-6"],
  [class*="bg-red-7"],
  div[class*="bg-red-5"],
  div[class*="bg-red-6"],
  div[class*="bg-red-7"],
  span[class*="bg-red-5"],
  span[class*="bg-red-6"],
  span[class*="bg-red-7"] {
    color: white !important; /* White text for red backgrounds */
  }

  /* Orange background elements get white text */
  [class*="bg-orange-4"],
  [class*="bg-orange-5"],
  [class*="bg-orange-6"],
  [class*="bg-orange-7"],
  div[class*="bg-orange-4"],
  div[class*="bg-orange-5"],
  div[class*="bg-orange-6"],
  div[class*="bg-orange-7"],
  span[class*="bg-orange-4"],
  span[class*="bg-orange-5"],
  span[class*="bg-orange-6"],
  span[class*="bg-orange-7"] {
    color: white !important; /* White text for orange backgrounds */
  }

  /* All dark background colors for any element */
  [class*="bg-blue-5"],
  [class*="bg-blue-6"],
  [class*="bg-blue-7"],
  [class*="bg-purple-5"],
  [class*="bg-purple-6"],
  [class*="bg-purple-7"],
  [class*="bg-indigo-5"],
  [class*="bg-indigo-6"],
  [class*="bg-indigo-7"],
  [class*="bg-gray-7"],
  [class*="bg-gray-8"],
  [class*="bg-gray-9"] {
    color: white !important; /* White text for all dark backgrounds */
  }

  /* Modern button styles */
  .btn-gradient {
    background: linear-gradient(135deg, oklch(0.65 0.25 280) 0%, oklch(0.60 0.22 260) 100%);
    @apply text-white border-0 shadow-sm hover:shadow-md transition-all duration-300;
    color: white !important;
  }

  .btn-gradient:hover {
    background: linear-gradient(135deg, oklch(0.60 0.22 280) 0%, oklch(0.55 0.20 260) 100%);
    transform: translateY(-1px);
    color: white !important;
  }

  /* Glass effect */
  .glass {
    backdrop-filter: blur(16px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Shadow utilities */
  .shadow-soft {
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 0.08);
  }

  .shadow-medium {
    box-shadow: 0 4px 12px 0 rgb(0 0 0 / 0.12);
  }

  .shadow-strong {
    box-shadow: 0 8px 24px 0 rgb(0 0 0 / 0.16);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

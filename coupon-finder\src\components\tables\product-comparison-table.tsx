/**
 * ProductComparison Table với TanStack Table v8
 * 
 * Features:
 * - Drag & drop reordering
 * - Responsive design
 * - Product comparison với advanced features
 * - Type-safe với Zod schemas
 */

import React, { useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  ColumnOrderState,
} from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Star,
  ShoppingCart,
  ExternalLink,
  Heart,
  Share2,
} from 'lucide-react';

// Types
interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  rating: number;
  reviews: number;
  image: string;
  store: string;
  affiliateLink: string;
  features?: string[];
  sold?: number;
  category?: string;
}

interface ProductComparisonTableProps {
  products: Product[];
  onAddToFavorites?: (productId: string) => void;
  onShare?: (productId: string) => void;
  className?: string;
}



export function ProductComparisonTable({
  products,
  onAddToFavorites,
  onShare,
  className,
}: ProductComparisonTableProps) {
  const [columnOrder, setColumnOrder] = useState<ColumnOrderState>([
    'criteria',
    ...products.map(p => p.id)
  ]);

  // Define comparison criteria
  const comparisonCriteria = [
    {
      id: 'product',
      label: 'Sản phẩm',
      render: (product: Product) => (
        <div className="space-y-2">
          <div>
            <img
              src={product.image}
              alt={product.name}
              className="w-20 h-20 object-cover rounded-lg mx-auto"
            />
          </div>
          <h3 className="font-medium text-sm line-clamp-2 text-center">
            {product.name}
          </h3>
          <p className="text-xs text-muted-foreground text-center">
            {product.store}
          </p>
        </div>
      ),
    },
    {
      id: 'price',
      label: 'Giá',
      render: (product: Product) => (
        <div className="text-center space-y-1">
          <div className="text-lg font-bold text-red-600">
            {product.price.toLocaleString('vi-VN')}đ
          </div>
          {product.originalPrice && (
            <div className="text-sm text-muted-foreground line-through">
              {product.originalPrice.toLocaleString('vi-VN')}đ
            </div>
          )}
          {product.discount && (
            <Badge variant="destructive" className="text-xs">
              -{product.discount}%
            </Badge>
          )}
        </div>
      ),
    },
    {
      id: 'rating',
      label: 'Đánh giá',
      render: (product: Product) => (
        <div className="text-center space-y-1">
          <div className="flex items-center justify-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="font-medium">{product.rating}</span>
          </div>
          <div className="text-xs text-muted-foreground">
            {product.reviews.toLocaleString('vi-VN')} đánh giá
          </div>
        </div>
      ),
    },
    {
      id: 'sold',
      label: 'Đã bán',
      render: (product: Product) => (
        <div className="text-center">
          <div className="font-medium">
            {product.sold ? product.sold.toLocaleString('vi-VN') : 'N/A'}
          </div>
        </div>
      ),
    },
    {
      id: 'features',
      label: 'Tính năng',
      render: (product: Product) => (
        <div className="space-y-1">
          {product.features?.slice(0, 3).map((feature, index) => (
            <div key={index} className="text-xs bg-muted px-2 py-1 rounded">
              {feature}
            </div>
          )) || <span className="text-muted-foreground text-xs">Không có</span>}
        </div>
      ),
    },
    {
      id: 'actions',
      label: 'Hành động',
      render: (product: Product) => (
        <div className="space-y-2">
          <Button 
            size="sm" 
            className="w-full"
            onClick={() => window.open(product.affiliateLink, '_blank')}
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            Mua ngay
          </Button>
          <div className="flex gap-1">
            {onAddToFavorites && (
              <Button
                variant="outline"
                size="sm"
                className="flex-1"
                onClick={() => onAddToFavorites(product.id)}
              >
                <Heart className="h-4 w-4" />
              </Button>
            )}
            {onShare && (
              <Button
                variant="outline"
                size="sm"
                className="flex-1"
                onClick={() => onShare(product.id)}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => window.open(product.affiliateLink, '_blank')}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ),
    },
  ];

  // Create columns dynamically
  const columns: ColumnDef<any>[] = [
    {
      id: 'criteria',
      header: 'Tiêu chí',
      cell: ({ row }) => (
        <div className="font-medium text-gray-900 min-w-[120px]">
          {comparisonCriteria[row.index]?.label}
        </div>
      ),
    },
    ...products.map((product) => ({
      id: product.id,
      header: () => (
        <div className="text-center min-w-[200px]">
          <div className="flex items-center justify-center gap-2">
            <span className="font-medium">Sản phẩm {products.findIndex(p => p.id === product.id) + 1}</span>
          </div>
        </div>
      ),
      cell: ({ row }) => (
        <div className="text-center">
          {comparisonCriteria[row.index]?.render(product)}
        </div>
      ),
    })),
  ];

  // Create data for table rows
  const tableData = comparisonCriteria.map((_, index) => ({ index }));

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      columnOrder,
    },
    onColumnOrderChange: setColumnOrder,
  });

  if (products.length === 0) {
    return (
      <Card className={`p-8 text-center ${className}`}>
        <div className="text-muted-foreground">
          <div className="text-4xl mb-4">📊</div>
          <h3 className="text-lg font-medium mb-2">
            Chưa có sản phẩm nào để so sánh
          </h3>
          <p className="text-sm">
            Thêm sản phẩm vào danh sách so sánh để bắt đầu
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">
          So sánh sản phẩm ({products.length}/4)
        </h2>
      </div>

      {/* Responsive Table Container */}
      <div className="rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="bg-muted/50">
                  {headerGroup.headers.map((header) => (
                    <TableHead 
                      key={header.id}
                      className="text-center font-semibold"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} className="border-b">
                  {row.getVisibleCells().map((cell) => (
                    <TableCell 
                      key={cell.id}
                      className="p-4 align-top"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile View */}
      <div className="lg:hidden space-y-4">
        <h3 className="font-medium">Xem trên mobile:</h3>
        <div className="grid gap-4">
          {products.map((product) => (
            <Card key={product.id} className="p-4">
              <div className="space-y-3">
                {comparisonCriteria.map((criteria) => (
                  <div key={criteria.id} className="flex justify-between items-start">
                    <span className="font-medium text-sm min-w-[80px]">
                      {criteria.label}:
                    </span>
                    <div className="flex-1 ml-2">
                      {criteria.render(product)}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}

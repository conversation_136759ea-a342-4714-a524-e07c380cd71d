import { createFileRoute } from '@tanstack/react-router';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Star,
  Clock,
  Flame,
  TrendingUp,
  ShoppingCart,
  ExternalLink,
  Timer,
  Users,
} from 'lucide-react';

export const Route = createFileRoute('/deals')({
  component: DealsPage,
});

function DealsPage() {
  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header Section */}
      <div className='text-center mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 mb-4'>
          🔥 Top Deals Hôm Nay
        </h1>
        <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
          Khám phá những deal hot nhất, sản phẩm bán chạy và ưu đãi có thời hạn
        </p>
      </div>

      {/* Category Tabs */}
      <div className='flex flex-wrap gap-2 justify-center mb-8'>
        {[
          'Tất cả',
          'Flash Sale',
          'Bán chạy',
          'Giá sốc',
          'Mới nhất',
          'Sắp hết hạn',
        ].map(category => (
          <Badge
            key={category}
            variant={category === 'Tất cả' ? 'default' : 'secondary'}
            className='px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors'
          >
            {category}
          </Badge>
        ))}
      </div>

      {/* Flash Sale Section */}
      <div className='mb-8'>
        <div className='flex items-center gap-3 mb-4'>
          <div className='flex items-center gap-2'>
            <Flame className='h-6 w-6 text-red-500' />
            <h2 className='text-2xl font-bold text-gray-900'>Flash Sale</h2>
          </div>
          <div className='flex items-center gap-2 text-red-600'>
            <Timer className='h-5 w-5' />
            <span className='font-mono font-bold'>02:15:30</span>
          </div>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          {flashSaleProducts.map(product => (
            <FlashSaleCard key={product.id} product={product} />
          ))}
        </div>
      </div>

      {/* Top Selling Section */}
      <div className='mb-8'>
        <div className='flex items-center gap-2 mb-4'>
          <TrendingUp className='h-6 w-6 text-green-500' />
          <h2 className='text-2xl font-bold text-gray-900'>
            Sản Phẩm Bán Chạy
          </h2>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {topSellingProducts.map((product, index) => (
            <TopSellingCard
              key={product.id}
              product={product}
              rank={index + 1}
            />
          ))}
        </div>
      </div>

      {/* Best Deals Section */}
      <div>
        <h2 className='text-2xl font-bold text-gray-900 mb-4'>
          Deal Tốt Nhất Hôm Nay
        </h2>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
          {bestDeals.map(product => (
            <DealCard key={product.id} product={product} />
          ))}
        </div>
      </div>

      {/* Load More Button */}
      <div className='text-center mt-8'>
        <Button variant='outline' size='lg'>
          Xem thêm deals
        </Button>
      </div>
    </div>
  );
}

function FlashSaleCard({ product }: { product: any }) {
  const soldPercentage = (product.sold / product.stock) * 100;

  return (
    <Card className='hover:shadow-lg transition-shadow duration-200 border-red-200'>
      <CardHeader className='pb-3'>
        <div className='relative'>
          <img
            src={product.image}
            alt={product.name}
            className='w-full h-40 object-cover rounded-lg mb-3'
          />
          <Badge variant='destructive' className='absolute top-2 right-2'>
            -{product.discount}%
          </Badge>
          <div className='absolute bottom-2 left-2 bg-red-600 text-white px-2 py-1 rounded text-xs font-bold'>
            FLASH SALE
          </div>
        </div>
        <CardTitle className='text-base font-semibold text-gray-900 line-clamp-2'>
          {product.name}
        </CardTitle>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Price */}
          <div className='flex items-center gap-2'>
            <span className='text-lg font-bold text-red-600'>
              {product.price.toLocaleString('vi-VN')}đ
            </span>
            <span className='text-sm text-gray-500 line-through'>
              {product.originalPrice.toLocaleString('vi-VN')}đ
            </span>
          </div>

          {/* Progress Bar */}
          <div className='space-y-1'>
            <div className='flex justify-between text-xs text-gray-600'>
              <span>Đã bán {product.sold}</span>
              <span>{soldPercentage.toFixed(0)}%</span>
            </div>
            <div className='w-full bg-gray-200 rounded-full h-2'>
              <div
                className='bg-red-500 h-2 rounded-full transition-all duration-300'
                style={{ width: `${soldPercentage}%` }}
              />
            </div>
          </div>

          {/* Action Button */}
          <Button className='w-full bg-red-600 hover:bg-red-700'>
            <ShoppingCart className='h-4 w-4 mr-2' />
            Mua ngay
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function TopSellingCard({ product, rank }: { product: any; rank: number }) {
  return (
    <Card className='hover:shadow-lg transition-shadow duration-200'>
      <CardHeader className='pb-3'>
        <div className='flex gap-3'>
          <div className='relative'>
            <img
              src={product.image}
              alt={product.name}
              className='w-20 h-20 object-cover rounded-lg'
            />
            <div className='absolute -top-2 -left-2 bg-yellow-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold'>
              {rank}
            </div>
          </div>
          <div className='flex-1'>
            <CardTitle className='text-base font-semibold text-gray-900 line-clamp-2'>
              {product.name}
            </CardTitle>
            <CardDescription className='text-sm text-gray-600 mt-1'>
              {product.store}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Price and Rating */}
          <div className='flex items-center justify-between'>
            <span className='text-lg font-bold text-red-600'>
              {product.price.toLocaleString('vi-VN')}đ
            </span>
            <div className='flex items-center gap-1'>
              <Star className='h-4 w-4 fill-yellow-400 text-yellow-400' />
              <span className='text-sm font-medium'>{product.rating}</span>
            </div>
          </div>

          {/* Sales Info */}
          <div className='flex items-center gap-2 text-sm text-gray-600'>
            <Users className='h-4 w-4' />
            <span>{product.monthlySales.toLocaleString('vi-VN')} đã bán</span>
          </div>

          {/* Action Buttons */}
          <div className='flex gap-2'>
            <Button className='flex-1' size='sm'>
              <ShoppingCart className='h-4 w-4 mr-2' />
              Mua ngay
            </Button>
            <Button variant='outline' size='sm' className='px-3'>
              <ExternalLink className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function DealCard({ product }: { product: any }) {
  return (
    <Card className='hover:shadow-lg transition-shadow duration-200'>
      <CardHeader className='pb-3'>
        <div className='relative'>
          <img
            src={product.image}
            alt={product.name}
            className='w-full h-48 object-cover rounded-lg mb-3'
          />
          <Badge variant='destructive' className='absolute top-2 right-2'>
            -{product.discount}%
          </Badge>
          {product.isLimitedTime && (
            <div className='absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-bold'>
              CÓ HẠN
            </div>
          )}
        </div>
        <CardTitle className='text-lg font-semibold text-gray-900 line-clamp-2'>
          {product.name}
        </CardTitle>
        <CardDescription className='text-sm text-gray-600'>
          {product.store}
        </CardDescription>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Price */}
          <div className='flex items-center gap-2'>
            <span className='text-xl font-bold text-red-600'>
              {product.price.toLocaleString('vi-VN')}đ
            </span>
            {product.originalPrice && (
              <span className='text-sm text-gray-500 line-through'>
                {product.originalPrice.toLocaleString('vi-VN')}đ
              </span>
            )}
          </div>

          {/* Rating and Reviews */}
          <div className='flex items-center gap-2'>
            <div className='flex items-center'>
              <Star className='h-4 w-4 fill-yellow-400 text-yellow-400' />
              <span className='text-sm font-medium ml-1'>{product.rating}</span>
            </div>
            <span className='text-sm text-gray-500'>
              ({product.reviews} đánh giá)
            </span>
          </div>

          {/* Expiry Time */}
          {product.expiryTime && (
            <div className='flex items-center gap-2 text-sm text-orange-600'>
              <Clock className='h-4 w-4' />
              <span>Còn {product.expiryTime}</span>
            </div>
          )}

          {/* Action Buttons */}
          <div className='flex gap-2 pt-2'>
            <Button className='flex-1'>
              <ShoppingCart className='h-4 w-4 mr-2' />
              Mua ngay
            </Button>
            <Button variant='outline' size='sm' className='px-3'>
              <ExternalLink className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Mock data for development
const flashSaleProducts = [
  {
    id: 1,
    name: 'Tai nghe Bluetooth Sony WH-1000XM5',
    price: 6990000,
    originalPrice: 8990000,
    discount: 22,
    image: '/api/placeholder/300/300',
    sold: 45,
    stock: 100,
  },
  {
    id: 2,
    name: 'Laptop ASUS ROG Strix G15',
    price: 18990000,
    originalPrice: 24990000,
    discount: 24,
    image: '/api/placeholder/300/300',
    sold: 12,
    stock: 50,
  },
  {
    id: 3,
    name: 'iPhone 15 128GB',
    price: 19990000,
    originalPrice: 22990000,
    discount: 13,
    image: '/api/placeholder/300/300',
    sold: 78,
    stock: 100,
  },
  {
    id: 4,
    name: 'Samsung Galaxy Watch 6',
    price: 4990000,
    originalPrice: 6990000,
    discount: 29,
    image: '/api/placeholder/300/300',
    sold: 23,
    stock: 80,
  },
];

const topSellingProducts = [
  {
    id: 1,
    name: 'Áo thun nam basic cotton',
    store: 'Fashion Store',
    price: 199000,
    rating: 4.8,
    monthlySales: 15420,
    image: '/api/placeholder/300/300',
  },
  {
    id: 2,
    name: 'Kem chống nắng Anessa',
    store: 'Beauty Shop',
    price: 450000,
    rating: 4.9,
    monthlySales: 12350,
    image: '/api/placeholder/300/300',
  },
  {
    id: 3,
    name: 'Giày sneaker Nike Air Force 1',
    store: 'Nike Store',
    price: 2890000,
    rating: 4.7,
    monthlySales: 8920,
    image: '/api/placeholder/300/300',
  },
];

const bestDeals = [
  {
    id: 1,
    name: 'MacBook Air M2 256GB',
    store: 'Apple Store',
    price: 24990000,
    originalPrice: 27990000,
    discount: 11,
    rating: 4.9,
    reviews: 2340,
    image: '/api/placeholder/300/300',
    isLimitedTime: true,
    expiryTime: '2 ngày',
  },
  {
    id: 2,
    name: 'Samsung Galaxy S24 256GB',
    store: 'Samsung Store',
    price: 18990000,
    originalPrice: 22990000,
    discount: 17,
    rating: 4.8,
    reviews: 1890,
    image: '/api/placeholder/300/300',
    isLimitedTime: false,
  },
  {
    id: 3,
    name: 'iPad Pro 11 inch M4 256GB',
    store: 'Apple Store',
    price: 26990000,
    originalPrice: 29990000,
    discount: 10,
    rating: 4.9,
    reviews: 1560,
    image: '/api/placeholder/300/300',
    isLimitedTime: true,
    expiryTime: '5 giờ',
  },
  {
    id: 4,
    name: 'Dyson V15 Detect Absolute',
    store: 'Dyson Store',
    price: 16990000,
    originalPrice: 19990000,
    discount: 15,
    rating: 4.7,
    reviews: 890,
    image: '/api/placeholder/300/300',
    isLimitedTime: false,
  },
];

import { createContext, useContext, ReactNode, useMemo, useCallback } from 'react'
import { useAuthStore } from '@/stores/auth-store'
import { useLogin, useLogout, useRegister, useCurrentUser } from '@/lib/query-hooks'
import type { AuthUser } from '@/lib/auth'

// Interface cho Auth Context
interface AuthContextType {
  // User state
  user: AuthUser | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Auth actions
  login: (email: string, password: string, rememberMe?: boolean) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  register: (email: string, password: string, name?: string) => Promise<{ success: boolean; error?: string }>
  
  // UI state
  showLoginModal: boolean
  showRegisterModal: boolean
  
  // UI actions
  openLoginModal: () => void
  closeLoginModal: () => void
  openRegisterModal: () => void
  closeRegisterModal: () => void
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth Provider component
interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  // Get state from auth store - use individual selectors to prevent unnecessary re-renders
  const user = useAuthStore((state) => state.user)
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const showLoginModal = useAuthStore((state) => state.showLoginModal)
  const showRegisterModal = useAuthStore((state) => state.showRegisterModal)
  const openLoginModal = useAuthStore((state) => state.openLoginModal)
  const closeLoginModal = useAuthStore((state) => state.closeLoginModal)
  const openRegisterModal = useAuthStore((state) => state.openRegisterModal)
  const closeRegisterModal = useAuthStore((state) => state.closeRegisterModal)

  // Get mutations from query hooks
  const loginMutation = useLogin()
  const logoutMutation = useLogout()
  const registerMutation = useRegister()

  // Get current user query without autoSync để tránh circular dependency
  const userQuery = useCurrentUser({ autoSync: false })

  // Auth actions - wrapped with useCallback to prevent re-renders
  const login = useCallback(async (email: string, password: string, rememberMe?: boolean) => {
    try {
      await loginMutation.mutateAsync({ email, password, rememberMe })
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: error?.message || 'Đăng nhập thất bại'
      }
    }
  }, [loginMutation])

  const logout = useCallback(async () => {
    try {
      await logoutMutation.mutateAsync()
    } catch (error) {
      console.error('Logout error:', error)
      // Even if logout fails on server, clear client state
      useAuthStore.getState().clearUser()
    }
  }, [logoutMutation])

  const register = useCallback(async (email: string, password: string, name?: string) => {
    try {
      await registerMutation.mutateAsync({ email, password, name })
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: error?.message || 'Đăng ký thất bại'
      }
    }
  }, [registerMutation])

  const contextValue: AuthContextType = useMemo(() => ({
    // State
    user,
    isAuthenticated,
    isLoading: userQuery.isLoading || loginMutation.isPending || logoutMutation.isPending || registerMutation.isPending,

    // Actions
    login,
    logout,
    register,

    // UI state
    showLoginModal,
    showRegisterModal,

    // UI actions - Zustand actions are stable, safe to use directly
    openLoginModal,
    closeLoginModal,
    openRegisterModal,
    closeRegisterModal,
  }), [
    // Only include values that can actually change
    user,
    isAuthenticated,
    userQuery.isLoading,
    loginMutation.isPending,
    logoutMutation.isPending,
    registerMutation.isPending,
    login,
    logout,
    register,
    showLoginModal,
    showRegisterModal,
    // REMOVED: Zustand UI actions are stable references, không cần include trong deps
    // openLoginModal, closeLoginModal, openRegisterModal, closeRegisterModal
  ])

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Export types
export type { AuthContextType }

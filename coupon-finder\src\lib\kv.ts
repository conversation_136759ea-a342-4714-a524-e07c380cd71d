/**
 * Cloudflare KV Store Utilities
 * Provides type-safe access to KV namespaces for caching, analytics, and configuration
 */

// KV Namespace Types
export interface KVNamespaces {
  CACHE: KVNamespace;
  ANALYTICS: KVNamespace;
  CONFIG: KVNamespace;
}

// Cache Keys
export const CACHE_KEYS = {
  // AccessTrade API responses
  COUPONS: (query: string) => `coupons:${query}`,
  PRODUCTS: (query: string) => `products:${query}`,
  CAMPAIGNS: () => 'campaigns:all',
  TOP_PRODUCTS: (category?: string) => `top-products:${category || 'all'}`,
  
  // User sessions
  USER_SESSION: (userId: string) => `session:${userId}`,
  
  // Rate limiting
  RATE_LIMIT: (ip: string) => `rate-limit:${ip}`,
} as const;

// Analytics Keys
export const ANALYTICS_KEYS = {
  CLICKS: (date: string) => `clicks:${date}`,
  VIEWS: (date: string) => `views:${date}`,
  CONVERSIONS: (date: string) => `conversions:${date}`,
  USER_ACTIVITY: (userId: string, date: string) => `activity:${userId}:${date}`,
} as const;

// Config Keys
export const CONFIG_KEYS = {
  FEATURED_PRODUCTS: 'featured-products',
  CATEGORIES: 'categories',
  SITE_SETTINGS: 'site-settings',
  API_SETTINGS: 'api-settings',
  CACHE_SETTINGS: 'cache-settings',
} as const;

// Cache TTL (Time To Live) in seconds
export const CACHE_TTL = {
  SHORT: 300,      // 5 minutes
  MEDIUM: 1800,    // 30 minutes
  LONG: 3600,      // 1 hour
  VERY_LONG: 86400, // 24 hours
} as const;

// KV Store Service Class
export class KVService {
  constructor(private kv: KVNamespaces) {}

  // Cache operations
  async getFromCache<T>(key: string): Promise<T | null> {
    try {
      const value = await this.kv.CACHE.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('KV Cache get error:', error);
      return null;
    }
  }

  async setCache<T>(key: string, value: T, ttl: number = CACHE_TTL.MEDIUM): Promise<void> {
    try {
      await this.kv.CACHE.put(key, JSON.stringify(value), {
        expirationTtl: ttl,
      });
    } catch (error) {
      console.error('KV Cache set error:', error);
    }
  }

  async deleteFromCache(key: string): Promise<void> {
    try {
      await this.kv.CACHE.delete(key);
    } catch (error) {
      console.error('KV Cache delete error:', error);
    }
  }

  // Analytics operations
  async recordAnalytics(key: string, data: any): Promise<void> {
    try {
      const existing = await this.kv.ANALYTICS.get(key);
      const analytics = existing ? JSON.parse(existing) : [];
      analytics.push({
        ...data,
        timestamp: new Date().toISOString(),
      });
      
      await this.kv.ANALYTICS.put(key, JSON.stringify(analytics));
    } catch (error) {
      console.error('KV Analytics record error:', error);
    }
  }

  async getAnalytics<T>(key: string): Promise<T[]> {
    try {
      const value = await this.kv.ANALYTICS.get(key);
      return value ? JSON.parse(value) : [];
    } catch (error) {
      console.error('KV Analytics get error:', error);
      return [];
    }
  }

  // Configuration operations
  async getConfig<T>(key: string): Promise<T | null> {
    try {
      const value = await this.kv.CONFIG.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('KV Config get error:', error);
      return null;
    }
  }

  async setConfig<T>(key: string, value: T): Promise<void> {
    try {
      await this.kv.CONFIG.put(key, JSON.stringify(value));
    } catch (error) {
      console.error('KV Config set error:', error);
    }
  }

  // Rate limiting
  async checkRateLimit(ip: string, limit: number = 100, window: number = 900): Promise<boolean> {
    try {
      const key = CACHE_KEYS.RATE_LIMIT(ip);
      const current = await this.kv.CACHE.get(key);
      const count = current ? parseInt(current) : 0;
      
      if (count >= limit) {
        return false; // Rate limit exceeded
      }
      
      await this.kv.CACHE.put(key, (count + 1).toString(), {
        expirationTtl: window,
      });
      
      return true; // Request allowed
    } catch (error) {
      console.error('KV Rate limit error:', error);
      return true; // Allow on error
    }
  }

  // Bulk operations
  async listKeys(namespace: keyof KVNamespaces, prefix?: string): Promise<string[]> {
    try {
      const result = await this.kv[namespace].list({ prefix });
      return result.keys.map(key => key.name);
    } catch (error) {
      console.error('KV List keys error:', error);
      return [];
    }
  }

  async clearNamespace(namespace: keyof KVNamespaces, prefix?: string): Promise<void> {
    try {
      const keys = await this.listKeys(namespace, prefix);
      const deletePromises = keys.map(key => this.kv[namespace].delete(key));
      await Promise.all(deletePromises);
    } catch (error) {
      console.error('KV Clear namespace error:', error);
    }
  }
}

// Helper function to create KV service instance
export function createKVService(env: any): KVService {
  return new KVService({
    CACHE: env.CACHE,
    ANALYTICS: env.ANALYTICS,
    CONFIG: env.CONFIG,
  });
}

// Type definitions for common data structures
export interface CachedCoupon {
  id: string;
  title: string;
  description: string;
  discount: string;
  code: string;
  expiryDate: string;
  affiliateLink: string;
  merchant: string;
  category: string;
  cachedAt: string;
}

export interface CachedProduct {
  id: string;
  title: string;
  price: number;
  originalPrice: number;
  discount: number;
  rating: number;
  sold: number;
  imageUrl: string;
  affiliateLink: string;
  merchant: string;
  category: string;
  cachedAt: string;
}

export interface AnalyticsEvent {
  type: 'click' | 'view' | 'conversion';
  productId?: string;
  couponId?: string;
  userId?: string;
  ip: string;
  userAgent: string;
  referrer?: string;
  timestamp: string;
}

export interface SiteConfig {
  siteName: string;
  siteDescription: string;
  featuredCategories: string[];
  maxProductsPerPage: number;
  cacheSettings: {
    defaultTTL: number;
    apiResponseTTL: number;
    staticContentTTL: number;
  };
  apiSettings: {
    accessTradeTimeout: number;
    maxRetries: number;
    rateLimitPerMinute: number;
  };
}

import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { cache } from 'hono/cache'
import type { Bindings, Variables } from '../hono-app'
import { createAccessTradeClient, AccessTradeError } from '../../lib/accesstrade-client'

// Validation schemas
const searchCouponsSchema = z.object({
  url: z.string().optional().refine(val => !val || val === '' || z.string().url().safeParse(val).success, {
    message: 'Invalid URL format'
  }),
  keyword: z.string().optional(),
  category: z.string().optional(),
  merchant: z.string().optional().default('shopee'),
  sortBy: z.enum(['discount', 'expiry', 'popularity', 'newest']).optional(),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),
  offset: z.string().transform(Number).pipe(z.number().min(0)).default('0'),
})

const getCouponSchema = z.object({
  id: z.string().min(1, 'Coupon ID is required'),
})

// Create coupons routes
export const couponsRoutes = new Hono<{
  Bindings: Bindings
  Variables: Variables
}>()

// Cache middleware for coupon data
couponsRoutes.use('/search', cache({
  cacheName: 'coupons-search',
  cacheControl: 'max-age=300', // 5 minutes
}))

// Get all coupons endpoint (default listing)
couponsRoutes.get('/',
  zValidator('query', z.object({
    category: z.string().optional(),
    merchant: z.string().optional(),
    limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).default('20'),
    offset: z.string().transform(Number).pipe(z.number().min(0)).default('0'),
  })),
  async (c) => {
    try {
      const { category, merchant, limit, offset } = c.req.valid('query')

      // Generate cache key
      const cacheKey = `coupons:list:${JSON.stringify({ category, merchant, limit, offset })}`

      // Try to get from cache first (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Coupons retrieved from cache',
            data,
            cached: true,
          })
        }
      }

      // AccessTrade API call
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

      const searchParams: any = {
        scope: 'all',
        status: 1,
      }

      if (merchant) {
        searchParams.merchant = merchant
      }

      if (category) {
        searchParams.categories = [category]
      }

      const offersResponse = await accessTradeClient.getCoupons(searchParams)

      // Transform data
      const coupons = {
        coupons: offersResponse.data.map(offer => ({
          id: offer.id,
          title: offer.name,
          description: offer.content,
          code: extractCouponCode(offer.content) || 'CLICK_TO_GET',
          discount: extractDiscountInfo(offer.content) || 'Ưu đãi đặc biệt',
          merchant: offer.merchant,
          category: offer.categories[0]?.category_name || category || 'general',
          expiryDate: offer.end_time || '2024-12-31',
          affiliateLink: offer.aff_link,
          imageUrl: offer.image,
          banners: offer.banners,
          domain: offer.domain,
          startDate: offer.start_time,
        })).slice(offset, offset + limit),
        total: offersResponse.total || 0,
        limit,
        offset,
      }

      // Cache the results (if env available)
      if (c.env?.ENABLE_CACHING === 'true') {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(coupons),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 300 }
        )
      }

      return c.json({
        success: true,
        message: 'Coupons retrieved successfully',
        data: coupons,
        cached: false,
      })

    } catch (error) {
      console.error('Get coupons error:', error)
      return c.json({
        error: 'Failed to get coupons',
        message: 'An error occurred while fetching coupons'
      }, 500)
    }
  }
)

// Search coupons endpoint
couponsRoutes.get('/search',
  zValidator('query', searchCouponsSchema),
  async (c) => {
    try {
      const { url, keyword, category, merchant, sortBy, limit, offset } = c.req.valid('query')
      
      // Generate cache key
      const cacheKey = `coupons:search:${JSON.stringify({ url, keyword, category, merchant, sortBy, limit, offset })}`
      
      // Try to get from cache first (skip in development)
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Coupons retrieved from cache',
            data,
            cached: true,
          })
        }
      }
      
      // Use AccessTrade API with fallback to mock data
      let offersResponse: any

      try {
        // Real AccessTrade API call
        const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

        // Determine search parameters based on input
        let searchParams: any = {}

        if (merchant) {
          searchParams.merchant = merchant
        }

        if (category) {
          searchParams.categories = [category]
        }

        // For URL-based search, use the new searchCouponsByUrl method
        if (url && url.trim() !== '') {
          console.log('Searching coupons by URL:', url)
          offersResponse = await accessTradeClient.searchCouponsByUrl(url)
          console.log('AccessTrade URL search response:', offersResponse)
        } else {
          // Use the correct endpoint for coupon search
          const queryParams: any = {
            limit: limit || 20,
            page: Math.floor((offset || 0) / (limit || 20)) + 1,
          }

          if (merchant && merchant.trim() !== '') queryParams.merchant = merchant
          if (keyword && keyword.trim() !== '') queryParams.keyword = keyword

          console.log('AccessTrade API call with params:', queryParams)

          // Use the makeRequest method directly for offers_informations/coupon
          const response = await accessTradeClient.makeRequest<any>('/offers_informations/coupon', queryParams)

          offersResponse = {
            data: response.data?.data || [],
            total: response.data?.count || 0,
          }

          console.log('AccessTrade coupon search response:', offersResponse)
        }

      } catch (error) {
        console.error('AccessTrade API error, falling back to mock data:', error)

        // Fallback to mock data if API fails
        offersResponse = {
          data: generateMockCoupons(keyword, category, merchant, limit),
          total: 50,
        }
      }

      // If AccessTrade API returns empty data, fallback to mock data
      if (!offersResponse.data || offersResponse.data.length === 0) {
        console.log('AccessTrade API returned empty data, using mock data')
        offersResponse = {
          data: generateMockCoupons(keyword, category, merchant, limit),
          total: 50,
        }
      }

      // Transform AccessTrade offers to our coupon format
      let transformedCoupons = offersResponse.data.map(offer => ({
        id: offer.id,
        title: offer.name,
        description: offer.content,
        code: extractCouponCode(offer.content) || 'CLICK_TO_GET',
        discount: extractDiscountInfo(offer.content) || 'Ưu đãi đặc biệt',
        merchant: offer.merchant,
        category: offer.categories[0]?.category_name || 'general',
        expiryDate: offer.end_time || '2024-12-31',
        affiliateLink: offer.aff_link,
        imageUrl: offer.image,
        banners: offer.banners,
        domain: offer.domain,
        startDate: offer.start_time,
      }))

      // Apply sorting if specified
      if (sortBy && sortBy !== '') {
        transformedCoupons = transformedCoupons.sort((a, b) => {
          switch (sortBy) {
            case 'discount':
              // Sort by discount amount (extract number from discount string)
              const aDiscount = parseFloat(a.discount.match(/\d+/)?.[0] || '0')
              const bDiscount = parseFloat(b.discount.match(/\d+/)?.[0] || '0')
              return bDiscount - aDiscount
            case 'expiry':
              // Sort by expiry date (soonest first)
              return new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime()
            case 'newest':
              // Sort by start date (newest first)
              return new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
            case 'popularity':
            default:
              // Default sorting (keep original order from AccessTrade)
              return 0
          }
        })
      }

      const coupons = {
        coupons: transformedCoupons.slice(offset, offset + limit), // Apply pagination
        total: offersResponse.total || 0,
        limit,
        offset,
        sortBy,
      }
      
      // Cache the results (skip in development)
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(coupons),
          { expirationTtl: parseInt(c.env.CACHE_TTL) || 3600 }
        )
      }

      // Track analytics (skip in development)
      if (c.env?.ENABLE_ANALYTICS === 'true') {
        await trackCouponSearch(c, { keyword, category, merchant, resultCount: coupons.coupons.length })
      }
      
      return c.json({
        success: true,
        message: 'Coupons retrieved successfully',
        data: coupons,
        cached: false,
      })
      
    } catch (error) {
      console.error('Search coupons error:', error)

      if (error instanceof AccessTradeError) {
        return c.json({
          error: 'AccessTrade API Error',
          message: error.message,
          status: error.status,
        }, error.status || 500)
      }

      return c.json({
        error: 'Search failed',
        message: 'An error occurred while searching for coupons'
      }, 500)
    }
  }
)

// Get hot/featured coupons (must be before /:id route)
couponsRoutes.get('/hot',
  async (c) => {
    try {
      const limit = parseInt(c.req.query('limit') || '20')
      const date = parseInt(c.req.query('date') || '1') as 1 | 2 // 1: tuần, 2: tháng

      // Generate cache key
      const cacheKey = `coupons:hot:${limit}:${date}`

      // Try to get from cache first
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Hot coupons retrieved from cache',
            data,
            cached: true,
          })
        }
      }

      // AccessTrade API call - Get hot coupons
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')
      const hotCouponsResponse = await accessTradeClient.getHotCoupons({ limit, date })

      // Transform to our coupon format
      const coupons = {
        coupons: hotCouponsResponse.data.map(offer => ({
          id: offer.id,
          title: offer.name,
          description: offer.content,
          code: extractCouponCode(offer.content) || 'CLICK_TO_GET',
          discount: extractDiscountInfo(offer.content) || 'Ưu đãi đặc biệt',
          merchant: offer.merchant,
          category: offer.categories[0]?.category_name || 'general',
          expiryDate: offer.end_time || '2024-12-31',
          affiliateLink: offer.aff_link,
          imageUrl: offer.image,
          banners: offer.banners,
          domain: offer.domain,
          startDate: offer.start_time,
          isHot: true,
        })),
        total: hotCouponsResponse.count,
        limit,
        date,
      }

      // Cache the results
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(coupons),
          { expirationTtl: parseInt(c.env?.CACHE_TTL || '1800') } // 30 minutes for hot coupons
        )
      }

      return c.json({
        success: true,
        message: 'Hot coupons retrieved successfully',
        data: coupons,
        cached: false,
      })

    } catch (error) {
      console.error('Get hot coupons error, falling back to mock data:', error)

      // Fallback to mock data if API fails
      const mockCoupons = generateMockCoupons(undefined, undefined, undefined, limit)

      const coupons = {
        coupons: mockCoupons.map(offer => ({
          id: offer.id,
          title: offer.name,
          description: offer.content,
          code: extractCouponCode(offer.content) || 'CLICK_TO_GET',
          discount: extractDiscountInfo(offer.content) || 'Ưu đãi đặc biệt',
          merchant: offer.merchant,
          category: offer.categories[0]?.category_name || 'general',
          expiryDate: offer.end_time || '2024-12-31',
          affiliateLink: offer.aff_link,
          imageUrl: offer.image,
          banners: offer.banners,
          domain: offer.domain,
          startDate: offer.start_time,
          isHot: true,
        })),
        total: mockCoupons.length,
        limit,
        date,
      }

      return c.json({
        success: true,
        message: 'Hot coupons retrieved successfully (fallback data)',
        data: coupons,
        cached: false,
      })
    }
  }
)

// Get coupon by ID
couponsRoutes.get('/:id',
  zValidator('param', getCouponSchema),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      
      // Generate cache key
      const cacheKey = `coupon:${id}`
      
      // Try to get from cache first
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Coupon retrieved from cache',
            data,
            cached: true,
          })
        }
      }
      
      // AccessTrade API call - Get specific offer by ID
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

      // Get all offers and find the one with matching ID
      const offersResponse = await accessTradeClient.getCoupons({ status: 1 })
      const offer = offersResponse.data.find(o => o.id === id)

      if (!offer) {
        return c.json({
          error: 'Coupon not found',
          message: 'The requested coupon was not found'
        }, 404)
      }

      // Transform to our coupon format
      const coupon = {
        id: offer.id,
        title: offer.name,
        description: offer.content,
        code: extractCouponCode(offer.content) || 'CLICK_TO_GET',
        discount: extractDiscountInfo(offer.content) || 'Ưu đãi đặc biệt',
        merchant: offer.merchant,
        category: offer.categories[0]?.category_name || 'general',
        expiryDate: offer.end_time || '2024-12-31',
        affiliateLink: offer.aff_link,
        imageUrl: offer.image,
        banners: offer.banners,
        domain: offer.domain,
        startDate: offer.start_time,
      }
      

      
      // Cache the result
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(coupon),
          { expirationTtl: parseInt(c.env?.CACHE_TTL || '3600') }
        )
      }
      
      return c.json({
        success: true,
        message: 'Coupon retrieved successfully',
        data: coupon,
        cached: false,
      })
      
    } catch (error) {
      console.error('Get coupon error:', error)

      if (error instanceof AccessTradeError) {
        return c.json({
          error: 'AccessTrade API Error',
          message: error.message,
          status: error.status,
        }, error.status || 500)
      }

      return c.json({
        error: 'Failed to get coupon',
        message: 'An error occurred while fetching coupon details'
      }, 500)
    }
  }
)

// Get coupons by category
couponsRoutes.get('/category/:category',
  async (c) => {
    try {
      const category = c.req.param('category')
      const limit = parseInt(c.req.query('limit') || '20')
      const offset = parseInt(c.req.query('offset') || '0')
      
      // Generate cache key
      const cacheKey = `coupons:category:${category}:${limit}:${offset}`
      
      // Try to get from cache first
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        const cached = await c.env.CACHE.get(cacheKey)
        if (cached) {
          const data = JSON.parse(cached)
          return c.json({
            success: true,
            message: 'Category coupons retrieved from cache',
            data,
            cached: true,
          })
        }
      }
      
      // AccessTrade API call - Get offers by category
      const accessTradeClient = createAccessTradeClient(c.env?.ACCESSTRADE_API_KEY || 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV')

      const offersResponse = await accessTradeClient.getCoupons({
        categories: [category],
        status: 1,
      })

      // Transform and paginate
      const coupons = {
        coupons: offersResponse.data.map(offer => ({
          id: offer.id,
          title: offer.name,
          description: offer.content,
          code: extractCouponCode(offer.content) || 'CLICK_TO_GET',
          discount: extractDiscountInfo(offer.content) || 'Ưu đãi đặc biệt',
          merchant: offer.merchant,
          category: offer.categories[0]?.category_name || category,
          expiryDate: offer.end_time || '2024-12-31',
          affiliateLink: offer.aff_link,
          imageUrl: offer.image,
          banners: offer.banners,
          domain: offer.domain,
          startDate: offer.start_time,
        })).slice(offset, offset + limit),
        total: offersResponse.total || 0,
        category,
        limit,
        offset,
      }
      
      // Cache the results
      if (c.env?.ENABLE_CACHING === 'true' && c.env?.CACHE) {
        await c.env.CACHE.put(
          cacheKey,
          JSON.stringify(coupons),
          { expirationTtl: parseInt(c.env?.CACHE_TTL || '3600') }
        )
      }
      
      return c.json({
        success: true,
        message: 'Category coupons retrieved successfully',
        data: coupons,
        cached: false,
      })
      
    } catch (error) {
      console.error('Get category coupons error:', error)
      return c.json({
        error: 'Failed to get category coupons',
        message: 'An error occurred while fetching category coupons'
      }, 500)
    }
  }
)



// Track coupon click/usage
couponsRoutes.post('/:id/track',
  zValidator('param', getCouponSchema),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const userAgent = c.req.header('User-Agent') || ''
      const referer = c.req.header('Referer') || ''

      // Track analytics
      if (c.env.ENABLE_ANALYTICS === 'true') {
        await trackCouponClick(c, { couponId: id, userAgent, referer })
      }

      return c.json({
        success: true,
        message: 'Coupon click tracked successfully'
      })

    } catch (error) {
      console.error('Track coupon error:', error)
      return c.json({
        error: 'Tracking failed',
        message: 'An error occurred while tracking coupon usage'
      }, 500)
    }
  }
)

// Helper functions for AccessTrade integration
function extractMerchantFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url)
    const hostname = urlObj.hostname.toLowerCase()

    // Map common domains to AccessTrade merchant names
    const merchantMap: Record<string, string> = {
      'shopee.vn': 'shopee',
      'lazada.vn': 'lazada',
      'tiki.vn': 'tikivn',
      'sendo.vn': 'sendo',
      'adayroi.com': 'adayroi',
      'fahasa.com': 'fahasa',
      'thegioididong.com': 'thegioididong',
    }

    for (const [domain, merchant] of Object.entries(merchantMap)) {
      if (hostname.includes(domain)) {
        return merchant
      }
    }

    return null
  } catch {
    return null
  }
}

function extractCouponCode(content: string): string | null {
  // Try to extract coupon code from content using regex patterns
  const patterns = [
    /mã\s*:?\s*([A-Z0-9]{3,20})/i,
    /code\s*:?\s*([A-Z0-9]{3,20})/i,
    /voucher\s*:?\s*([A-Z0-9]{3,20})/i,
    /\b([A-Z0-9]{4,15})\b/g,
  ]

  for (const pattern of patterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      return match[1].toUpperCase()
    }
  }

  return null
}

function extractDiscountInfo(content: string): string | null {
  // Try to extract discount information from content
  const patterns = [
    /giảm\s*(\d+%)/i,
    /giảm\s*(\d+k)/i,
    /giảm\s*(\d+\.\d+k)/i,
    /(\d+%)\s*off/i,
    /save\s*(\d+%)/i,
    /discount\s*(\d+%)/i,
  ]

  for (const pattern of patterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      return match[1]
    }
  }

  return null
}

async function trackCouponSearch(c: any, data: any) {
  // Store analytics in KV
  const timestamp = new Date().toISOString()
  const analyticsKey = `analytics:search:${timestamp}:${c.get('requestId')}`
  
  await c.env.ANALYTICS.put(analyticsKey, JSON.stringify({
    type: 'coupon_search',
    timestamp,
    requestId: c.get('requestId'),
    ...data,
  }))
}

async function trackCouponClick(c: any, data: any) {
  // Store analytics in KV
  const timestamp = new Date().toISOString()
  const analyticsKey = `analytics:click:${timestamp}:${c.get('requestId')}`

  await c.env.ANALYTICS.put(analyticsKey, JSON.stringify({
    type: 'coupon_click',
    timestamp,
    requestId: c.get('requestId'),
    ...data,
  }))
}

// Mock data generator for development
function generateMockCoupons(keyword?: string, category?: string, merchant?: string, limit: number = 20) {
  const mockCoupons = [
    {
      id: '1',
      name: 'Giảm 50% cho đơn hàng đầu tiên',
      content: 'Giảm 50% tối đa 100k cho đơn hàng đầu tiên. Mã: FIRST50',
      image: 'https://via.placeholder.com/300x200?text=Shopee+50%25',
      link: 'https://shopee.vn',
      aff_link: 'https://shopee.vn?aff=123',
      merchant: 'shopee',
      domain: 'shopee.vn',
      start_time: '2024-01-01T00:00:00Z',
      end_time: '2024-12-31T23:59:59Z',
      categories: [{ category_name: 'fashion', category_name_show: 'Thời trang', category_no: 1 }],
      banners: [{ link: 'https://via.placeholder.com/300x200', width: 300, height: 200 }],
    },
    {
      id: '2',
      name: 'Freeship toàn quốc',
      content: 'Miễn phí vận chuyển cho đơn hàng từ 99k. Mã: FREESHIP99',
      image: 'https://via.placeholder.com/300x200?text=Freeship',
      link: 'https://shopee.vn',
      aff_link: 'https://shopee.vn?aff=124',
      merchant: 'shopee',
      domain: 'shopee.vn',
      start_time: '2024-01-01T00:00:00Z',
      end_time: '2024-12-31T23:59:59Z',
      categories: [{ category_name: 'electronics', category_name_show: 'Điện tử', category_no: 2 }],
      banners: [{ link: 'https://via.placeholder.com/300x200', width: 300, height: 200 }],
    },
    {
      id: '3',
      name: 'Giảm 30% sản phẩm thời trang',
      content: 'Giảm 30% tối đa 200k cho sản phẩm thời trang. Mã: FASHION30',
      image: 'https://via.placeholder.com/300x200?text=Fashion+30%25',
      link: 'https://shopee.vn',
      aff_link: 'https://shopee.vn?aff=125',
      merchant: 'shopee',
      domain: 'shopee.vn',
      start_time: '2024-01-01T00:00:00Z',
      end_time: '2024-12-31T23:59:59Z',
      categories: [{ category_name: 'fashion', category_name_show: 'Thời trang', category_no: 1 }],
      banners: [{ link: 'https://via.placeholder.com/300x200', width: 300, height: 200 }],
    },
    {
      id: '4',
      name: 'Cashback 15% cho điện tử',
      content: 'Hoàn tiền 15% tối đa 500k cho sản phẩm điện tử. Mã: TECH15',
      image: 'https://via.placeholder.com/300x200?text=Tech+15%25',
      link: 'https://shopee.vn',
      aff_link: 'https://shopee.vn?aff=126',
      merchant: 'shopee',
      domain: 'shopee.vn',
      start_time: '2024-01-01T00:00:00Z',
      end_time: '2024-12-31T23:59:59Z',
      categories: [{ category_name: 'electronics', category_name_show: 'Điện tử', category_no: 2 }],
      banners: [{ link: 'https://via.placeholder.com/300x200', width: 300, height: 200 }],
    },
    {
      id: '5',
      name: 'Giảm 25% sản phẩm làm đẹp',
      content: 'Giảm 25% tối đa 150k cho sản phẩm làm đẹp. Mã: BEAUTY25',
      image: 'https://via.placeholder.com/300x200?text=Beauty+25%25',
      link: 'https://shopee.vn',
      aff_link: 'https://shopee.vn?aff=127',
      merchant: 'shopee',
      domain: 'shopee.vn',
      start_time: '2024-01-01T00:00:00Z',
      end_time: '2024-12-31T23:59:59Z',
      categories: [{ category_name: 'beauty', category_name_show: 'Làm đẹp', category_no: 3 }],
      banners: [{ link: 'https://via.placeholder.com/300x200', width: 300, height: 200 }],
    },
  ]

  // Filter by keyword if provided
  let filteredCoupons = mockCoupons
  if (keyword) {
    filteredCoupons = mockCoupons.filter(coupon =>
      coupon.name.toLowerCase().includes(keyword.toLowerCase()) ||
      coupon.content.toLowerCase().includes(keyword.toLowerCase())
    )
  }

  // Filter by category if provided
  if (category && category !== 'all') {
    filteredCoupons = filteredCoupons.filter(coupon =>
      coupon.categories.some(cat => cat.category_name === category)
    )
  }

  // Filter by merchant if provided
  if (merchant && merchant !== 'all') {
    filteredCoupons = filteredCoupons.filter(coupon => coupon.merchant === merchant)
  }

  // Duplicate coupons to reach the limit if needed
  while (filteredCoupons.length < limit && filteredCoupons.length > 0) {
    const originalLength = filteredCoupons.length
    filteredCoupons = [...filteredCoupons, ...filteredCoupons.slice(0, Math.min(originalLength, limit - filteredCoupons.length))]
  }

  return filteredCoupons.slice(0, limit)
}

export default couponsRoutes

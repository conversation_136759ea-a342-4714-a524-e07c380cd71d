import { Context, Next } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { HTTPException } from 'hono/http-exception'
import { ZodSchema, ZodError } from 'zod'
import { ValidationError } from './error-handler'

/**
 * Enhanced Zod validator with custom error handling
 */
export function createValidator<T extends ZodSchema>(
  target: 'json' | 'form' | 'query' | 'param' | 'header',
  schema: T,
  options?: {
    customErrorMessage?: string
    throwOnError?: boolean
    logErrors?: boolean
  }
) {
  return zValidator(target, schema, (result, c) => {
    const { customErrorMessage, throwOnError = false, logErrors = true } = options || {}
    
    if (!result.success) {
      if (logErrors) {
        console.error('Validation failed:', {
          target,
          path: c.req.path,
          method: c.req.method,
          errors: result.error.issues,
          requestId: c.get('requestId'),
        })
      }

      const firstIssue = result.error.issues[0]
      const message = customErrorMessage || firstIssue?.message || 'Validation failed'
      
      if (throwOnError) {
        throw new ValidationError(
          message,
          firstIssue?.path.join('.'),
          firstIssue?.code
        )
      }

      // Return custom error response
      throw new HTTPException(400, {
        message: 'Validation Error',
        cause: {
          error: 'Validation Error',
          message,
          field: firstIssue?.path.join('.') || undefined,
          code: firstIssue?.code,
          details: result.error.issues.map(issue => ({
            field: issue.path.join('.'),
            message: issue.message,
            code: issue.code,
          })),
          timestamp: new Date().toISOString(),
          path: c.req.path,
          method: c.req.method,
          requestId: c.get('requestId'),
        }
      })
    }
  })
}

/**
 * JSON body validator
 */
export function validateJson<T extends ZodSchema>(
  schema: T,
  options?: {
    customErrorMessage?: string
    throwOnError?: boolean
    logErrors?: boolean
  }
) {
  return createValidator('json', schema, options)
}

/**
 * Form data validator
 */
export function validateForm<T extends ZodSchema>(
  schema: T,
  options?: {
    customErrorMessage?: string
    throwOnError?: boolean
    logErrors?: boolean
  }
) {
  return createValidator('form', schema, options)
}

/**
 * Query parameters validator
 */
export function validateQuery<T extends ZodSchema>(
  schema: T,
  options?: {
    customErrorMessage?: string
    throwOnError?: boolean
    logErrors?: boolean
  }
) {
  return createValidator('query', schema, options)
}

/**
 * Path parameters validator
 */
export function validateParam<T extends ZodSchema>(
  schema: T,
  options?: {
    customErrorMessage?: string
    throwOnError?: boolean
    logErrors?: boolean
  }
) {
  return createValidator('param', schema, options)
}

/**
 * Headers validator
 */
export function validateHeader<T extends ZodSchema>(
  schema: T,
  options?: {
    customErrorMessage?: string
    throwOnError?: boolean
    logErrors?: boolean
  }
) {
  return createValidator('header', schema, options)
}

/**
 * Content-Type validation middleware
 */
export function validateContentType(expectedTypes: string[]) {
  return async (c: Context, next: Next) => {
    const contentType = c.req.header('content-type')
    
    if (!contentType) {
      throw new HTTPException(400, {
        message: 'Content-Type header is required'
      })
    }

    const isValidType = expectedTypes.some(type => 
      contentType.toLowerCase().includes(type.toLowerCase())
    )

    if (!isValidType) {
      throw new HTTPException(415, {
        message: `Unsupported Media Type. Expected: ${expectedTypes.join(', ')}`
      })
    }

    await next()
  }
}

/**
 * Request size validation middleware
 */
export function validateRequestSize(maxSizeBytes: number) {
  return async (c: Context, next: Next) => {
    const contentLength = c.req.header('content-length')
    
    if (contentLength) {
      const size = parseInt(contentLength, 10)
      if (size > maxSizeBytes) {
        throw new HTTPException(413, {
          message: `Request too large. Maximum size: ${maxSizeBytes} bytes`
        })
      }
    }

    await next()
  }
}

/**
 * Custom validation middleware for complex validation logic
 */
export function customValidation<T = any>(
  validator: (data: T, c: Context) => Promise<boolean> | boolean,
  errorMessage: string = 'Validation failed'
) {
  return async (c: Context, next: Next) => {
    try {
      const data = await c.req.json() as T
      const isValid = await validator(data, c)
      
      if (!isValid) {
        throw new ValidationError(errorMessage)
      }
      
      await next()
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error
      }
      throw new ValidationError('Invalid request data')
    }
  }
}

/**
 * Sanitization middleware
 */
export function sanitizeInput() {
  return async (c: Context, next: Next) => {
    // Basic XSS protection - remove script tags and dangerous attributes
    const sanitizeString = (str: string): string => {
      return str
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
    }

    const sanitizeObject = (obj: any): any => {
      if (typeof obj === 'string') {
        return sanitizeString(obj)
      }
      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject)
      }
      if (obj && typeof obj === 'object') {
        const sanitized: any = {}
        for (const [key, value] of Object.entries(obj)) {
          sanitized[key] = sanitizeObject(value)
        }
        return sanitized
      }
      return obj
    }

    // Sanitize request body if it exists
    try {
      const contentType = c.req.header('content-type')
      if (contentType?.includes('application/json')) {
        const body = await c.req.json()
        const sanitizedBody = sanitizeObject(body)
        
        // Store sanitized body in context for later use
        c.set('sanitizedBody', sanitizedBody)
      }
    } catch (error) {
      // If JSON parsing fails, continue without sanitization
    }

    await next()
  }
}

/**
 * Rate limiting validation
 */
export function validateRateLimit(
  maxRequests: number,
  windowMs: number,
  keyGenerator?: (c: Context) => string
) {
  const requests = new Map<string, { count: number; resetTime: number }>()

  return async (c: Context, next: Next) => {
    const key = keyGenerator ? keyGenerator(c) : c.req.header('x-forwarded-for') || 'default'
    const now = Date.now()
    const windowStart = now - windowMs

    // Clean up old entries
    for (const [k, v] of requests.entries()) {
      if (v.resetTime < windowStart) {
        requests.delete(k)
      }
    }

    const current = requests.get(key) || { count: 0, resetTime: now + windowMs }
    
    if (current.count >= maxRequests && current.resetTime > now) {
      const retryAfter = Math.ceil((current.resetTime - now) / 1000)
      
      const response = new Response(JSON.stringify({
        error: 'Rate Limit Exceeded',
        message: `Too many requests. Try again in ${retryAfter} seconds.`,
        retryAfter,
        timestamp: new Date().toISOString(),
      }), {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': retryAfter.toString(),
          'X-RateLimit-Limit': maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': current.resetTime.toString(),
        }
      })
      
      return response
    }

    current.count++
    requests.set(key, current)

    // Add rate limit headers
    c.res.headers.set('X-RateLimit-Limit', maxRequests.toString())
    c.res.headers.set('X-RateLimit-Remaining', (maxRequests - current.count).toString())
    c.res.headers.set('X-RateLimit-Reset', current.resetTime.toString())

    await next()
  }
}

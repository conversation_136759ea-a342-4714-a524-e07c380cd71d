# Wrangler configuration for Cloudflare Workers deployment with Hono.dev
name = "coupon-finder"
main = ".vinxi/build/server/index.mjs"
compatibility_date = "2024-12-27"
compatibility_flags = ["nodejs_compat"]

# Hono.dev specific configuration
# Enable streaming for better performance
send_metrics = false

# Static Assets configuration (Workers Static Assets)
# [assets]
# directory = ".vinxi/build/client"
# binding = "ASSETS"
# not_found_handling = "single-page-application"

# Build configuration
[build]
command = "pnpm run build:cf"

# Environment variables
[vars]
NODE_ENV = "production"
APP_NAME = "Coupon Finder"
APP_VERSION = "1.0.0"
API_TIMEOUT = "30000"
CACHE_TTL = "3600"
ENABLE_ANALYTICS = "true"
ENABLE_CACHING = "true"
ENABLE_DEBUG = "false"
RATE_LIMIT_REQUESTS = "100"
RATE_LIMIT_WINDOW = "900000"
LOG_LEVEL = "info"
LOG_FORMAT = "json"

# Development configuration
[env.development]
name = "coupon-finder-dev"

[env.development.vars]
NODE_ENV = "development"
APP_URL = "https://coupon-finder-dev.your-subdomain.workers.dev"
ENABLE_DEBUG = "true"
LOG_LEVEL = "debug"

# Production configuration
[env.production]
name = "coupon-finder-prod"

[env.production.vars]
NODE_ENV = "production"
APP_URL = "https://coupon-finder-prod.your-subdomain.workers.dev"
ENABLE_DEBUG = "false"
LOG_LEVEL = "warn"

# KV namespaces
[[kv_namespaces]]
binding = "CACHE"
id = "d3882ce881fc45d6a5a38adcb0e26bff"

[[kv_namespaces]]
binding = "ANALYTICS"
id = "9b1953563e1d40f7a689a40670953e67"

[[kv_namespaces]]
binding = "CONFIG"
id = "997077cb5f214f34941aab033aadc6bc"

# D1 databases
[[d1_databases]]
binding = "DB"
database_name = "coupon-finder-db"
database_id = "685ffd13-3b89-41bd-ab38-c6ece5c57a9d"
migrations_dir = "drizzle/migrations"

# R2 buckets (nếu cần)
# [[r2_buckets]]
# binding = "STORAGE"
# bucket_name = "coupon-finder-storage"

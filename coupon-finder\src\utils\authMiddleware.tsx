import { createMiddleware } from '@tanstack/react-start'
import { AuthService, type AuthUser, type AuthSession } from '@/lib/auth'
import { getDb } from '@/db'

export const authMiddleware = createMiddleware()
  .server(async ({ next, context }) => {
    try {
      // Get database instance
      const db = getDb()
      const authService = new AuthService(db)

      // Clean expired sessions periodically
      await authService.cleanExpiredSessions()

      // Get token from cookies or Authorization header
      let token: string | null = null

      // Try to get token from cookies first
      if (context.request) {
        const cookieHeader = context.request.headers.get('cookie')
        if (cookieHeader) {
          const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
            const [name, value] = cookie.trim().split('=')
            if (name && value) {
              acc[name] = decodeURIComponent(value)
            }
            return acc
          }, {} as Record<string, string>)

          token = cookies['auth-token'] || null
        }
      }

      // If no token in cookies, try Authorization header
      if (!token && context.request) {
        const authHeader = context.request.headers.get('authorization')
        if (authHeader && authHeader.startsWith('Bearer ')) {
          token = authHeader.substring(7)
        }
      }

      let user: AuthUser | null = null
      let session: AuthSession | null = null

      // Validate token and get user
      if (token) {
        session = await authService.getSessionWithUser(token)
        user = session?.user || null
      }

      // Pass auth context to next middleware/handler
      return next({
        context: {
          auth: {
            user,
            session,
            isAuthenticated: !!user,
            token,
          },
          authService,
        },
      })
    } catch (error) {
      console.error('Auth middleware error:', error)

      // Continue with no auth if there's an error
      return next({
        context: {
          auth: {
            user: null,
            session: null,
            isAuthenticated: false,
            token: null,
          },
          authService: null,
        },
      })
    }
  })

// Type definitions for auth context
export interface AuthContext {
  user: AuthUser | null
  session: AuthSession | null
  isAuthenticated: boolean
  token: string | null
}

// Extend TanStack Start middleware context
declare module '@tanstack/react-start' {
  interface MiddlewareContext {
    auth?: AuthContext
    authService?: AuthService | null
  }
}

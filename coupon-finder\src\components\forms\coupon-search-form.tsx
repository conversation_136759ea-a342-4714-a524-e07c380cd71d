/**
 * Coupon Search Form Component
 * 
 * Sử dụng TanStack Form với Zod v4 validation
 * Demo implementation cho T1.3.6 và T1.3.8
 */

import { useEffect } from 'react';
import { useForm } from '@tanstack/react-form';
import { Search, Link, Tag } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { 
  FormTextInput, 
  FormSelect, 
  FormField 
} from './form-field';
import { 
  couponSearchSchema, 
  type CouponSearchFormData 
} from '@/lib/validation-schemas';
import { 
  useZodForm, 
  createZodValidator, 
  fieldValidators,
  type FormSubmissionResult 
} from '@/lib/form-utils';

// ===== TYPE DEFINITIONS =====

export interface CouponSearchFormProps {
  onSubmit: (data: CouponSearchFormData) => Promise<FormSubmissionResult<CouponSearchFormData>>;
  onSearchTypeChange?: (searchType: 'url' | 'keyword') => void;
  defaultValues?: Partial<CouponSearchFormData>;
  isLoading?: boolean;
  className?: string;
}

// ===== CONSTANTS =====

const SEARCH_TYPE_OPTIONS = [
  { value: 'keyword', label: 'Tìm theo từ khóa' },
  { value: 'url', label: 'Tìm theo URL Shopee' }
];

const CATEGORY_OPTIONS = [
  { value: '', label: 'Tất cả danh mục' },
  { value: 'fashion', label: 'Thời trang' },
  { value: 'electronics', label: 'Điện tử' },
  { value: 'beauty', label: 'Làm đẹp' },
  { value: 'home', label: 'Nhà cửa & Đời sống' },
  { value: 'sports', label: 'Thể thao' },
  { value: 'books', label: 'Sách' },
  { value: 'food', label: 'Thực phẩm' }
];

const SORT_OPTIONS = [
  { value: 'discount', label: 'Giảm giá cao nhất' },
  { value: 'expiry', label: 'Sắp hết hạn' },
  { value: 'popularity', label: 'Phổ biến nhất' }
];

// ===== MAIN COMPONENT =====

export function CouponSearchForm({
  onSubmit,
  onSearchTypeChange,
  defaultValues = {},
  isLoading = false,
  className
}: CouponSearchFormProps) {
  // Sử dụng custom useZodForm hook với type-safe validation
  const form = useZodForm({
    schema: couponSearchSchema,
    defaultValues: {
      searchType: 'keyword',
      searchValue: '',
      category: '',
      sortBy: 'discount',
      ...defaultValues
    } as CouponSearchFormData,
    onSubmit,
    onSubmitInvalid: (errors) => {
      console.error('Form validation errors:', errors);
    }
  });

  // Watch for search type changes
  useEffect(() => {
    const searchType = form.state.values.searchType;
    if (searchType) {
      onSearchTypeChange?.(searchType);
    }
  }, [form.state.values.searchType, onSearchTypeChange]);

  return (
    <Card className={cn("p-6", className)}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('📝 Form onSubmit triggered');
          console.log('📝 Form values:', form.state.values);
          console.log('📝 Form isValid:', form.state.isValid);
          console.log('📝 Form errors:', form.state.errors);

          form.handleSubmit();
        }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center space-x-2">
          <Search className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">Tìm kiếm mã giảm giá</h2>
        </div>

        {/* Search Type Selection */}
        <form.Field
          name="searchType"
          validators={{
            onChange: createZodValidator(couponSearchSchema.shape.searchType)
          }}
        >
          {(field) => (
            <FormSelect
              field={field}
              label="Loại tìm kiếm"
              options={SEARCH_TYPE_OPTIONS}
              required
            />
          )}
        </form.Field>

        {/* Search Value Input */}
        <form.Field
          name="searchValue"
          validators={{
            onChange: ({ value }) => {
              // Custom validation dựa trên search type
              const searchType = form.getFieldValue('searchType');
              
              if (!value || value.trim() === '') {
                return 'Vui lòng nhập từ khóa hoặc URL';
              }

              if (searchType === 'url') {
                try {
                  const url = new URL(value);
                  if (!url.hostname.includes('shopee.vn')) {
                    return 'Chỉ hỗ trợ URL từ Shopee.vn';
                  }
                } catch {
                  return 'URL không hợp lệ';
                }
              } else if (searchType === 'keyword') {
                if (value.length < 2) {
                  return 'Từ khóa phải có ít nhất 2 ký tự';
                }
              }

              return undefined;
            },
            onBlur: fieldValidators.required('Trường này là bắt buộc')
          }}
        >
          {(field) => {
            const searchType = form.getFieldValue('searchType');
            const isUrlSearch = searchType === 'url';
            
            return (
              <FormField
                field={field}
                label={isUrlSearch ? "URL sản phẩm Shopee" : "Từ khóa tìm kiếm"}
                description={
                  isUrlSearch 
                    ? "Ví dụ: https://shopee.vn/product-name-i.123.456"
                    : "Ví dụ: áo thun, điện thoại, mỹ phẩm"
                }
                required
              >
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2">
                    {isUrlSearch ? (
                      <Link className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Tag className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                  <input
                    id={field.name}
                    name={field.name}
                    type="text"
                    value={field.state.value || ''}
                    onChange={(e) => field.handleChange(e.target.value)}
                    onBlur={field.handleBlur}
                    placeholder={
                      isUrlSearch 
                        ? "Dán URL sản phẩm Shopee vào đây..."
                        : "Nhập từ khóa sản phẩm..."
                    }
                    className={cn(
                      "flex h-12 w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                      !field.state.meta.isValid && field.state.meta.isTouched && "border-red-500 focus-visible:ring-red-500"
                    )}
                  />
                </div>
              </FormField>
            );
          }}
        </form.Field>

        {/* Advanced Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Category Filter */}
          <form.Field
            name="category"
            validators={{
              onChange: createZodValidator(couponSearchSchema.shape.category)
            }}
          >
            {(field) => (
              <FormSelect
                field={field}
                label="Danh mục"
                options={CATEGORY_OPTIONS}
                placeholder="Chọn danh mục"
              />
            )}
          </form.Field>

          {/* Sort By */}
          <form.Field
            name="sortBy"
            validators={{
              onChange: createZodValidator(couponSearchSchema.shape.sortBy)
            }}
          >
            {(field) => (
              <FormSelect
                field={field}
                label="Sắp xếp theo"
                options={SORT_OPTIONS}
                required
              />
            )}
          </form.Field>
        </div>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 sm:flex-none"
          >
            {isLoading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                Đang tìm kiếm...
              </>
            ) : (
              <>
                <Search className="mr-2 h-4 w-4" />
                Tìm mã giảm giá
              </>
            )}
          </Button>

          <Button
            type="button"
            variant="outline"
            onClick={() => form.reset()}
            disabled={isLoading}
          >
            Đặt lại
          </Button>
        </div>

        {/* Form Debug Info (Development only) */}
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4">
            <summary className="cursor-pointer text-sm text-gray-500">
              Debug Info (Development)
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify({
                values: form.state.values,
                errors: form.state.errors,
                isValid: form.state.isValid,
                isSubmitting: form.state.isSubmitting
              }, null, 2)}
            </pre>
          </details>
        )}
      </form>
    </Card>
  );
}

// ===== USAGE EXAMPLE =====

/**
 * Example usage của CouponSearchForm
 */
export function CouponSearchFormExample() {
  const handleSubmit = async (data: CouponSearchFormData): Promise<FormSubmissionResult<CouponSearchFormData>> => {
    try {
      console.log('Searching for coupons with data:', data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock success response
      return {
        success: true,
        data,
        message: 'Tìm kiếm thành công!'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Có lỗi xảy ra khi tìm kiếm'
      };
    }
  };

  const handleSearchTypeChange = (searchType: 'url' | 'keyword') => {
    console.log('Search type changed to:', searchType);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <CouponSearchForm
        onSubmit={handleSubmit}
        onSearchTypeChange={handleSearchTypeChange}
        defaultValues={{
          searchType: 'keyword',
          sortBy: 'discount'
        }}
      />
    </div>
  );
}

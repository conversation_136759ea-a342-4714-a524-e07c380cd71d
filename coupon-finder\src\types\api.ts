// API Response Types for TanStack Start API Routes

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  meta?: {
    timestamp: string;
    source?: string;
    [key: string]: any;
  };
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

// Coupon Types
export interface Coupon {
  id: string;
  title: string;
  code: string;
  discount: string;
  discountType: 'fixed' | 'percentage' | 'shipping';
  minOrder: string;
  merchant: string;
  category: string;
  expiryDate: string;
  description: string;
  affiliateLink: string;
  isActive: boolean;
  usageCount: number;
  maxUsage: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CouponsResponse extends PaginatedResponse<Coupon> {
  filters?: {
    category?: string;
    merchant?: string;
    sortBy?: string;
    activeOnly?: boolean;
  };
}

// Category Types
export interface Category {
  id: string;
  name: string;
  slug: string;
  icon: string;
  description: string;
  isActive: boolean;
  couponCount?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CategoriesResponse extends ApiResponse<Category[]> {}

// Product Types
export interface Product {
  id: string;
  title: string;
  price: number;
  originalPrice: number;
  discount: number;
  rating: number;
  sold: number;
  imageUrl: string;
  merchant: string;
  category: string;
  affiliateLink: string;
  description: string;
  features: string[];
  inStock: boolean;
  freeShipping: boolean;
  rank?: number;
  trending?: boolean;
  commission?: number;
}

export interface ProductSearchResponse extends PaginatedResponse<Product> {
  filters: {
    query?: string;
    category?: string;
    minPrice: number;
    maxPrice: number;
    sortBy: string;
    inStockOnly: boolean;
    freeShippingOnly: boolean;
  };
}

export interface TopSellingProductsResponse extends PaginatedResponse<Product> {
  filters: {
    category?: string;
    trendingOnly: boolean;
    timeframe: string;
  };
}

// Campaign Types
export interface Campaign {
  id: string;
  name: string;
  merchant: string;
  merchantLogo: string;
  bannerUrl: string;
  description: string;
  commissionRate: number;
  commissionType: 'percentage' | 'fixed';
  cookieDuration: number;
  startDate: string;
  endDate: string;
  category: string;
  status: 'active' | 'inactive' | 'expired';
  affiliateLink: string;
  terms: string[];
  highlights: string[];
  isHot: boolean;
  participantCount: number;
}

export interface CampaignsResponse extends PaginatedResponse<Campaign> {
  filters: {
    category?: string;
    merchant?: string;
    status: string;
    hotOnly: boolean;
  };
}

// Affiliate Link Types
export interface AffiliateLink {
  id?: string;
  originalUrl: string;
  affiliateLink: string;
  campaignId: string;
  trackingId: string;
  affiliateId: string;
  customParams: Record<string, any>;
  createdAt: string;
  expiresAt: string;
  clickCount: number;
  conversionCount: number;
  commission: number;
  status: 'active' | 'inactive' | 'expired';
}

export interface CreateAffiliateLinkRequest {
  originalUrl: string;
  campaignId: string;
  customParams?: Record<string, any>;
}

export interface AffiliateLinksResponse
  extends PaginatedResponse<AffiliateLink> {}

// URL Parser Types
export interface ParsedUrl {
  platform: 'shopee' | 'lazada' | 'tiki';
  productId: string;
  shopId?: string;
  category: string;
  originalUrl: string;
  isValid: boolean;
  parsedAt: string;
  suggestions: {
    searchKeywords: string[];
    relatedCategories: string[];
    recommendedCoupons: string[];
  };
}

export interface ParseUrlRequest {
  url: string;
}

export interface ParseUrlResponse extends ApiResponse<ParsedUrl> {}

// Admin Types
export interface FeaturedProduct {
  id: string;
  productId: string;
  title: string;
  imageUrl: string;
  affiliateLink: string;
  categoryId: string;
  priority: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  clicks: number;
  conversions: number;
  revenue: number;
}

export interface AdminProductsResponse
  extends PaginatedResponse<FeaturedProduct> {}

// Analytics Types
export interface AnalyticsOverview {
  totalClicks: number;
  totalConversions: number;
  totalRevenue: number;
  conversionRate: number;
  averageOrderValue: number;
  topPerformingCategory: string;
  growthRate: {
    clicks: number;
    conversions: number;
    revenue: number;
  };
}

export interface DailyStat {
  date: string;
  clicks: number;
  conversions: number;
  revenue: number;
}

export interface TopProduct {
  id: string;
  title: string;
  clicks: number;
  conversions: number;
  revenue: number;
  conversionRate: number;
}

export interface CategoryStat {
  category: string;
  clicks: number;
  conversions: number;
  revenue: number;
  conversionRate: number;
}

export interface TrafficSource {
  source: string;
  clicks: number;
  percentage: number;
}

export interface Analytics {
  overview: AnalyticsOverview;
  dailyStats: DailyStat[];
  topProducts: TopProduct[];
  categoryStats: CategoryStat[];
  trafficSources: TrafficSource[];
}

export interface AnalyticsResponse extends ApiResponse<Analytics> {
  meta: {
    timeframe: string;
    category?: string;
    timestamp: string;
    source?: string;
  };
}

export interface AnalyticsEvent {
  eventType: 'click' | 'view' | 'conversion';
  productId: string;
  affiliateLink?: string;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  revenue?: number;
  customData?: Record<string, any>;
}

// Health Check Types
export interface HealthResponse
  extends ApiResponse<{
    status: string;
    timestamp: string;
    service: string;
    version: string;
  }> {}

// Error Types
export interface ApiError {
  error: string;
  message?: string;
  code?: string;
  details?: any;
}

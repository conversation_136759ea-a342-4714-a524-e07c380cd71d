# Task Breakdown
## Shopee Coupon Finder & Product Comparison Platform

### Sprint 1: TanStack Start Setup & Infrastructure (Week 1)

#### Task 1.1: TanStack Start Project Initialization ✅ HOÀN THÀNH
- **T1.1.1** Setup TanStack Start project với TypeScript ✅ HOÀN THÀNH
- **T1.1.2** Configure Tailwind CSS v4 ✅ HOÀN THÀNH
- **T1.1.3** Setup Shadcn/ui components ✅ HOÀN THÀNH
- **T1.1.4** Configure TanStack Router v1 với file-based routing ✅ HOÀN THÀNH
- **T1.1.5** Setup TanStack Start API routes ✅ HOÀN THÀNH
- **T1.1.6** Setup Cloudflare workers deployment ✅ HOÀN THÀNH
- **T1.1.7** Configure ESLint, <PERSON>ttier, <PERSON><PERSON> ✅ HOÀN THÀNH
- **T1.1.8** Setup environment variables cho TanStack Start ✅ HOÀN THÀNH
- **Estimate:** 2 days
- **Priority:** High
- **Dependencies:** None

#### Task 1.2: Database & Complete Authentication System ⭐ OPTIMIZED
- **T1.2.1** Setup Cloudflare D1 database ✅ HOÀN THÀNH
- **T1.2.2** Install và configure Drizzle ORM ✅ HOÀN THÀNH
- **T1.2.3** Design database schema với Drizzle schema ✅ HOÀN THÀNH
- **T1.2.4** Create D1 tables với Drizzle migrations ✅ HOÀN THÀNH
- **T1.2.5** Setup Complete Authentication System (User + Admin) ✅ HOÀN THÀNH
  - TanStack Start authentication
  - Auth middleware cho TanStack Router
  - Admin role-based access control
  - Protected routes với TanStack Router
- **T1.2.6** Configure Cloudflare KV store ✅ HOÀN THÀNH
- **Estimate:** 3 days (gộp auth tasks)
- **Priority:** High
- **Dependencies:** T1.1
- **Optimization:** Gộp T1.2.5, T1.2.6, T6.1.1-T6.1.5 thành authentication system hoàn chỉnh

#### Task 1.3: TanStack Suite Integration ⭐ OPTIMIZED
- **T1.3.1** Setup Zustand stores (auth, products, coupons, ui) ✅ HOÀN THÀNH
- **T1.3.2** Configure TanStack Query với TanStack Start ✅ HOÀN THÀNH
- **T1.3.3** ✅ REFACTOR: Gộp Authentication Layer - Loại bỏ trùng lặp ✅ HOÀN THÀNH
- **T1.3.4** ✅ REFACTOR: Tối ưu Data Fetching - Phân chia Zustand/TanStack Query ✅ HOÀN THÀNH
- **T1.3.5** ✅ REFACTOR: Gộp Provider Layer - AppProvider duy nhất ✅ HOÀN THÀNH
- **T1.3.6** Setup TanStack Form với type-safe validation ✅ HOÀN THÀNH
  - ✅ Cài đặt @tanstack/react-form và zod@^3.25.0
  - ✅ Tạo validation schemas với Zod v4 features
  - ✅ Tạo form utilities và hooks
  - ✅ Tạo reusable form components
  - ✅ Tích hợp FormProvider vào AppProvider
  - ✅ Thay thế tất cả form cũ bằng TanStack Form
  - ✅ Tạo demo page để test implementation
- **T1.3.7** Complete TanStack Table v8 Setup (All Tables) ✅ HOÀN THÀNH
  - ✅ Cài đặt @tanstack/react-table@8.21.3
  - ✅ Base table configuration với advanced features (DataTable component)
  - ✅ ProductComparison table với advanced comparison features
  - ✅ Admin ProductManagement table với bulk operations
  - ✅ Responsive design cho tất cả tables
  - ✅ Type-safe table components với TypeScript
  - ✅ Advanced features: sorting, filtering, pagination, column resizing
  - ✅ Refactor existing comparison page để sử dụng TanStack Table
  - ✅ UI components infrastructure (table.tsx, dropdown-menu.tsx)
- **T1.3.8** Create Zod v4 schemas cho data validation ✅ HOÀN THÀNH
  - ✅ Tạo schemas cho authentication (login, register)
  - ✅ Tạo schemas cho coupon search và product comparison
  - ✅ Tạo schemas cho admin management
  - ✅ Tạo schemas cho contact và feedback forms
  - ✅ Sử dụng Zod v4 features: z.email(), z.url(), error handling
  - ✅ Type-safe schema inference và validation utilities
- **T1.3.10** Setup AccessTrade API client với TanStack Start API routes ✅ HOÀN THÀNH
- **T1.3.11** Create API error handling utilities ✅ HOÀN THÀNH
- **T1.3.12** Configure TanStack Query devtools ✅ HOÀN THÀNH
- **📤 PUSHED TO GITHUB** - Commit: `b8d1a08` - T1.3.11 & T1.3.12 completed
- **Estimate:** 3 days
- **Priority:** High
- **Dependencies:** T1.2
- **Optimization:** Gộp T1.3.7, T4.2.1, T6.2.1 thành table setup hoàn chỉnh

#### Task 1.4: Complete Hono.dev Integration + AccessTrade API ⭐ OPTIMIZED
- **T1.4.1** Install và configure Hono.dev cho Cloudflare Workers ✅ HOÀN THÀNH
  - ✅ Cài đặt hono@4.7.11 và @hono/zod-validator@0.7.0
  - ✅ Tạo Hono app instance với TypeScript types (Bindings, Variables)
  - ✅ Setup middleware stack: CORS, Logger, Security Headers, Compression, Timing
  - ✅ Tạo API routes structure: auth, coupons, products, campaigns, analytics, admin
  - ✅ Implement JWT authentication với bcryptjs
  - ✅ Zod validation cho tất cả API endpoints
  - ✅ Cloudflare Workers bindings support (D1, KV, Environment variables)
  - ✅ Type-safe RPC client với hono/client
  - ✅ TanStack Start integration bridge (/api/$ route)
  - ✅ Error handling và 404 responses
  - ✅ Caching strategies với KV store
  - ✅ Analytics tracking system
  - ✅ Admin routes với role-based access control
  - ✅ Demo component để test API endpoints
  - ✅ Cập nhật wrangler.toml configuration
  - ✅ **MIGRATION HOÀN THÀNH**: Thay thế tất cả fetch() calls bằng Hono RPC client
    - ✅ Migrated `src/lib/query-client.ts` - Sử dụng api.coupons.search() và api.products.search()
    - ✅ Migrated `src/lib/query-hooks.ts` - Sử dụng api.auth.login(), api.products.getTopSelling(), api.campaigns.getAll()
    - ✅ Migrated `src/stores/coupons-store.ts` - Sử dụng api.coupons.search(), api.coupons.getByCategory()
    - ✅ Cleanup duplicate API structure - Xóa old TanStack Start API routes
    - ✅ Removed duplicate `hono-bridge.ts` file
- **T1.4.2** Setup Hono app instance với TypeScript types ✅ HOÀN THÀNH (gộp vào T1.4.1)
- **T1.4.3** Create Hono middleware cho authentication, CORS, logging ✅ HOÀN THÀNH (gộp vào T1.4.1)
- **T1.4.4** Setup Hono RPC client cho type-safe API calls ✅ HOÀN THÀNH (gộp vào T1.4.1)
- **T1.4.5** Complete AccessTrade API Implementation: ✅ HOÀN THÀNH
  - ✅ Create AccessTrade API client (`src/lib/accesstrade-client.ts`)
  - ✅ Implement getCoupons() với offers_informations API và Zod validation
  - ✅ Implement searchProducts() với datafeeds API và caching
  - ✅ Implement createAffiliateLink() với deep link generation
  - ✅ Implement getTopSellingProducts() với top_products API và performance optimization
  - ✅ Implement getCampaigns() với campaigns API và error handling
  - ✅ Update Hono routes để sử dụng AccessTrade API thực thay vì mock data
  - ✅ Add comprehensive error handling với AccessTradeError class
  - ✅ Implement helper functions cho URL parsing, merchant mapping, data transformation
  - ✅ Add type-safe Zod schemas cho tất cả AccessTrade API responses
  - ✅ Integrate caching strategies với Cloudflare KV cho performance
  - ✅ Add analytics tracking cho API usage và user behavior
- **T1.4.6** Integrate Hono với Cloudflare D1 database bindings ✅ HOÀN THÀNH
  - ✅ Cải thiện database utilities với caching và performance optimization
  - ✅ Tạo database middleware cho Hono (connection, health check, metrics)
  - ✅ Cập nhật auth routes để sử dụng database từ context
  - ✅ Tạo database test routes với health check, stats, connection info
  - ✅ Tích hợp database middleware vào Hono app
  - ✅ Cập nhật health endpoint với database status
  - ✅ Tạo DatabaseDemo component để test integration
  - ✅ Cập nhật Hono RPC client với database endpoints
  - ✅ Tạo database-demo route để test functionality
  - ✅ Sửa lỗi schema mismatch - cập nhật database routes để sử dụng đúng tables
  - ✅ Cập nhật database demo component với correct table names
  - ✅ Sửa lỗi API client calls và type definitions
  - ✅ **CLEANUP COMPLETED**: Xóa tất cả file test/demo không cần thiết
    - ✅ Xóa database-demo.tsx, hono-demo.tsx, error-handling-demo.tsx
    - ✅ Xóa query-examples.tsx, store-examples.tsx
    - ✅ Xóa routes: database-demo.tsx, hono-demo.tsx, query-test.tsx, db-status.tsx
    - ✅ Xóa lib/db-test.ts, stores/test-stores.ts
    - ✅ Xóa api/middleware/database.ts, api/routes/database.ts
    - ✅ Cleanup hono-app.ts và hono-client.ts imports
    - ✅ Dự án đã sạch sẽ, không còn file test/demo
- **T1.4.7** Setup Hono error handling và validation middleware ✅ HOÀN THÀNH
  - ✅ Tạo custom error classes (ValidationError, DatabaseError, AccessTradeError, AuthenticationError, AuthorizationError, RateLimitError)
  - ✅ Global error handler với HTTPException support
  - ✅ Enhanced Zod validation middleware với custom error handling
  - ✅ Security middleware (JWT auth, role-based access, API key auth, CSRF protection, rate limiting)
  - ✅ Request validation middleware (content-type, request size, sanitization)
  - ✅ Database middleware với health checks, metrics, transactions
- **T1.4.8** Create bridge giữa TanStack Start và Hono API ✅ HOÀN THÀNH
  - ✅ Request/Response transformation utilities
  - ✅ Bridge class với timeout, retry logic, metrics collection
  - ✅ TanStack Start API route handler integration
  - ✅ Type-safe bridge với error handling
  - ✅ Custom request/response transformations support
- **T1.4.9** Setup Hono routing và middleware stack ✅ HOÀN THÀNH
  - ✅ Organized middleware theo thứ tự ưu tiên (security → validation → database → caching)
  - ✅ Enhanced Variables interface với database và security variables
  - ✅ Middleware composition utilities và conditional middleware
  - ✅ Predefined middleware stacks (security, auth, database, validation)
- **T1.4.10** Integrate Hono với Cloudflare Workers bindings (D1, KV, R2) ✅ HOÀN THÀNH
  - ✅ Cloudflare bindings middleware (D1, KV, R2, Environment variables)
  - ✅ KV operations wrapper với error handling
  - ✅ R2 operations wrapper với comprehensive interface
  - ✅ Environment variables helper với type safety
  - ✅ Caching middleware sử dụng KV store
  - ✅ Analytics middleware với KV storage
- **T1.4.11** Create Hono type-safe client cho frontend consumption ✅ HOÀN THÀNH
  - ✅ Enhanced HonoClientOptions với timeout, retries, interceptors
  - ✅ Retry logic với exponential backoff
  - ✅ Request/Response interceptors support
  - ✅ Error handling callbacks và success callbacks
  - ✅ Type-safe RPC client với enhanced configuration
- **T1.4.12** Setup Hono OpenAPI documentation generation
- **T1.4.13** Configure Hono cho Cloudflare Workers deployment
- **T1.4.14** Test Hono API performance và type safety
- **Estimate:** 4 days (gộp T1.4 + T2.1)
- **Priority:** Critical
- **Dependencies:** T1.3
- **Optimization:** Gộp T1.4 và T2.1 thành complete Hono integration
- **Lý do:** Hono cung cấp ultrafast performance, type-safe RPC, và tối ưu cho Cloudflare Workers

### Sprint 2: Caching & Performance Optimization (Week 2)

#### Task 2.2A: Caching Strategy Implementation ⭐ OPTIMIZED
- **T2.2A.1** Create data transformation utilities với Hono helpers
- **T2.2A.2** Implement TanStack Query caching strategies với Hono RPC
- **T2.2A.3** Setup Cloudflare KV cho persistent caching trong Hono middleware
- **T2.2A.4** Setup cache invalidation strategies với Hono cache middleware
- **Estimate:** 1.5 days
- **Priority:** High
- **Dependencies:** T1.4

#### Task 2.2B: Performance & Security Optimization ⭐ OPTIMIZED
- **T2.2B.1** Setup Hono rate limiting middleware cho API protection
- **T2.2B.2** Integrate Hono streaming responses cho large datasets
- **T2.2B.3** Setup Hono compression middleware cho performance
- **Estimate:** 1 day
- **Priority:** High
- **Dependencies:** T2.2A

#### Task 2.2C: Data Sync & Error Handling ⭐ OPTIMIZED
- **T2.2C.1** Create Hono scheduled jobs cho data sync với Cloudflare Cron
- **T2.2C.2** Implement error retry logic với Hono error handlers và TanStack Query
- **Estimate:** 1 day
- **Priority:** High
- **Dependencies:** T2.2A
- **Optimization:** Chia T2.2 thành 3 tasks nhỏ để dễ quản lý (3.5 days total thay vì 2.5 days)

### Sprint 3: Core Features - Search & Coupons (Week 3)

#### Task 3.1A: Search Components Development ⭐ OPTIMIZED ✅ HOÀN THÀNH
- **T3.1A.1** Create SearchForm component với TanStack Form và Hono RPC client ✅ HOÀN THÀNH
  - ✅ CouponSearchForm đã có và hoạt động với TanStack Form
  - ✅ Kết nối với Hono RPC client thông qua api.coupons.search()
  - ✅ Form validation với Zod schemas
  - ✅ Type-safe form submission với FormSubmissionResult
- **T3.1A.2** Implement URL parsing logic cho Shopee links với Hono validation ✅ HOÀN THÀNH
  - ✅ Tạo parseShopeeUrl() function trong src/lib/url-parser.ts
  - ✅ Support multiple Shopee URL patterns (product, shop, category, search)
  - ✅ URL validation và error handling
  - ✅ Extract product name và keywords từ URL
  - ✅ Integration với search form cho URL search type
- **T3.1A.3** Create CouponCard component với Shadcn/ui và type-safe data từ Hono ✅ HOÀN THÀNH
  - ✅ CouponCard component đã có với modern UI design
  - ✅ Copy-to-clipboard functionality
  - ✅ Responsive design với hover effects
  - ✅ Support expired coupons display
  - ✅ Integration với search results page
- **T3.1A.4** Create search results page với TanStack Query và navigation ✅ HOÀN THÀNH
  - ✅ Tạo /search-results route với TanStack Router
  - ✅ TanStack Query integration cho data fetching
  - ✅ Search filters (category, sort) với real-time updates
  - ✅ Grid/List view modes
  - ✅ Loading, error, và empty states
  - ✅ URL parsing info display cho URL searches
- **T3.1A.5** Update homepage search integration ✅ HOÀN THÀNH
  - ✅ Replace mock API calls với real Hono API integration
  - ✅ Navigation to search results page after form submission
  - ✅ Error handling và user feedback
  - ✅ URL validation cho Shopee links
- **Estimate:** 1.5 days ✅ COMPLETED
- **Priority:** Critical
- **Dependencies:** T1.4
- **📤 IMPLEMENTATION COMPLETED** - Search functionality hoàn toàn hoạt động với Hono API
- **🔧 BUG FIXES COMPLETED:**
  - ✅ Fixed `fetchFeaturedProducts is not a function` error trong stores/index.ts
  - ✅ Fixed `Cannot read properties of undefined (reading 'search')` error trong coupons store
  - ✅ Added mock data generator cho development testing
  - ✅ Updated Hono app routes để enable API endpoints
  - ✅ Fixed dynamic imports trong stores để avoid undefined errors

#### Task 3.1B: Coupon Features Implementation ⭐ OPTIMIZED ✅ HOÀN THÀNH

#### 🔧 AccessTrade API Integration Issues Fixed ✅ HOÀN THÀNH

**Vấn đề đã khắc phục:**
1. **Missing UI Dependencies**: Thiếu `@radix-ui/react-dialog` và `@radix-ui/react-select` packages
2. **AccessTrade API Response Structure**: API trả về `{ data: [...] }` chứ không phải array trực tiếp
3. **Schema Validation**: Cập nhật Zod schema để phù hợp với dữ liệu thực tế từ AccessTrade API

**Các thay đổi đã thực hiện:**
- ✅ Cài đặt missing dependencies: `pnpm add @radix-ui/react-dialog @radix-ui/react-select`
- ✅ Sửa AccessTrade client để xử lý đúng response structure trong `getCoupons()` method
- ✅ Cập nhật `AccessTradeOfferSchema` để hỗ trợ:
  - `category_no` có thể là string hoặc number
  - `banners` array có thể rỗng (optional)
  - `coupons` field từ API response
- ✅ Test AccessTrade API endpoints và xác nhận hoạt động đúng

**Kết quả:**
- AccessTrade API hoạt động bình thường với endpoint `/offers_informations`
- API trả về dữ liệu thực từ các merchants như Shopee, Tiki, etc.
- UI components đã load được sau khi cài đặt dependencies
- Fallback mechanism vẫn hoạt động khi API lỗi
- **T3.1B.1** Implement CouponList với TanStack Query và Hono RPC pagination ✅ HOÀN THÀNH
  - ✅ Tạo CouponList component với TanStack Query integration
  - ✅ Pagination với page controls và navigation
  - ✅ Real-time filters (category, merchant, sortBy)
  - ✅ Grid/List view modes với responsive design
  - ✅ Loading, error, và empty states
  - ✅ Hono RPC client integration cho data fetching
- **T3.1B.2** Add copy-to-clipboard functionality với Hono analytics tracking ✅ HOÀN THÀNH
  - ✅ Copy-to-clipboard với visual feedback
  - ✅ Analytics tracking cho copy actions
  - ✅ Affiliate link tracking với external link opens
  - ✅ Error handling và user feedback
  - ✅ Integration với Hono analytics API
- **T3.1B.3** Create coupon detail modal với Hono real-time data ✅ HOÀN THÀNH
  - ✅ CouponDetailModal component với Dialog UI
  - ✅ Real-time data fetching với TanStack Query
  - ✅ Auto-refresh every minute khi modal mở
  - ✅ Comprehensive coupon information display
  - ✅ Action tracking (view, copy, click)
  - ✅ Responsive design với mobile support
- **T3.1B.4** AccessTrade API Integration ✅ HOÀN THÀNH
  - ✅ Replaced mock data với real AccessTrade API
  - ✅ API key configuration: txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV
  - ✅ Fallback to mock data nếu API fails
  - ✅ Error handling và logging
  - ✅ Data transformation từ AccessTrade format
- **T3.1B.5** Homepage Integration ✅ HOÀN THÀNH
  - ✅ Replaced sample coupons với real CouponList
  - ✅ Modal integration cho coupon details
  - ✅ Featured coupons section với AccessTrade data
  - ✅ Improved user experience với real data
- **Estimate:** 1.5 days ✅ COMPLETED
- **Priority:** Critical
- **Dependencies:** T3.1A
- **📤 IMPLEMENTATION COMPLETED** - Coupon features hoàn toàn hoạt động với AccessTrade API
- **🔧 UI COMPONENTS FIXES:**
  - ✅ Created Dialog component với Radix UI (@radix-ui/react-dialog)
  - ✅ Created Select component với Radix UI (@radix-ui/react-select)
  - ✅ Fixed import errors trong CouponDetailModal và CouponList
  - ✅ Added proper dependencies và exports
  - ✅ All components now working without errors

#### Task 3.1C: Integration & Real-time Features ⭐ OPTIMIZED
- **T3.1C.1** Integrate với Hono API routes thông qua type-safe RPC client
- **T3.1C.2** Setup TanStack Router navigation với Hono route parameters
- **T3.1C.3** Implement Hono WebSocket cho real-time coupon updates
- **Estimate:** 1 day
- **Priority:** Critical
- **Dependencies:** T3.1B
- **Optimization:** Chia T3.1 thành 3 tasks nhỏ để dễ quản lý (4 days total thay vì 3.5 days)

#### Task 3.2: Category-based Coupons
- **T3.2.1** Create CategoryTabs component
- **T3.2.2** Implement category filtering logic
- **T3.2.3** Create CategoryCouponGrid component
- **T3.2.4** Add search within category
- **T3.2.5** Implement sort by discount/expiry
- **Estimate:** 2 days
- **Priority:** High
- **Dependencies:** T3.1

### Sprint 4: Product Comparison Feature (Week 4)

#### Task 4.1: Product Search & Display
- **T4.1.1** Create ProductSearchForm component
- **T4.1.2** Implement ProductCard component
- **T4.1.3** Create ProductGrid với selection checkboxes
- **T4.1.4** Add product filtering (price, rating, discount)
- **T4.1.5** Implement infinite scroll pagination
- **Estimate:** 3 days
- **Priority:** Critical
- **Dependencies:** T2.1

#### Task 4.2: Product Comparison Table Implementation ⭐ OPTIMIZED
- **T4.2.1** Create ProductComparisonTable component (sử dụng T1.3.7 table setup)
- **T4.2.2** Implement drag & drop reordering
- **T4.2.3** Add comparison criteria (price, features, ratings)
- **T4.2.4** Create export comparison functionality
- **T4.2.5** Add affiliate link tracking với analytics
- **T4.2.6** Implement responsive table design
- **Estimate:** 2 days (giảm từ 2.5 days do đã setup table ở T1.3.7)
- **Priority:** High
- **Dependencies:** T4.1, T1.3.7
- **Optimization:** Loại bỏ T4.2.1 vì đã gộp vào T1.3.7

### Sprint 5: Top Products & Campaigns (Week 5)

#### Task 5.1: Top Selling Products
- **T5.1.1** Create TopProductsGrid component
- **T5.1.2** Implement trending products algorithm
- **T5.1.3** Add category filters cho top products
- **T5.1.4** Create product detail pages
- **T5.1.5** Implement related products suggestions
- **Estimate:** 2 days
- **Priority:** Medium
- **Dependencies:** T2.1

#### Task 5.2: Campaigns Page
- **T5.2.1** Create CampaignCard component
- **T5.2.2** Implement CampaignsGrid layout
- **T5.2.3** Add campaign filtering (merchant, category)
- **T5.2.4** Create campaign detail pages
- **T5.2.5** Implement campaign participation tracking
- **Estimate:** 2 days
- **Priority:** Medium
- **Dependencies:** T2.1

#### Task 5.3: Homepage Integration
- **T5.3.1** Create hero section với search
- **T5.3.2** Add featured coupons section
- **T5.3.3** Implement top products carousel
- **T5.3.4** Create categories quick access
- **T5.3.5** Add recent campaigns section
- **Estimate:** 1 day
- **Priority:** High
- **Dependencies:** T3.1, T4.1, T5.1, T5.2

### Sprint 6: Admin Dashboard với Cloudflare D1 (Week 6)

#### Task 6.1: Admin Dashboard Layout ⭐ OPTIMIZED
- **T6.1.1** Create admin dashboard layout (auth đã hoàn thành ở T1.2.5)
- **T6.1.2** Add admin navigation sidebar
- **T6.1.3** Setup admin-specific routes và middleware
- **Estimate:** 1 day (giảm từ 2 days do auth đã hoàn thành)
- **Priority:** High
- **Dependencies:** T1.2
- **Optimization:** Loại bỏ auth tasks vì đã gộp vào T1.2.5

#### Task 6.2: Product Management Implementation ⭐ OPTIMIZED
- **T6.2.1** Create ProductManagementTable component (sử dụng T1.3.7 table setup)
- **T6.2.2** Implement add/edit/delete products với database operations
- **T6.2.3** Add bulk operations (import/export) với Workers
- **T6.2.4** Create product approval workflow
- **T6.2.5** Implement featured products management
- **T6.2.6** Review database operations strategy (Drizzle vs MCP server)
- **T6.2.7** Create type-safe database operations
- **Estimate:** 2.5 days (giảm từ 3 days do table setup đã hoàn thành)
- **Priority:** High
- **Dependencies:** T6.1, T1.3.7
- **Optimization:** Loại bỏ table setup vì đã gộp vào T1.3.7

### Sprint 7: Analytics & Optimization (Week 7)

#### Task 7.1: Analytics Dashboard với Cloudflare Analytics
- **T7.1.1** Create analytics data collection trong KV
- **T7.1.2** Implement click tracking cho affiliate links
- **T7.1.3** Create conversion tracking với Workers
- **T7.1.4** Build analytics charts với recharts
- **T7.1.5** Add real-time metrics dashboard
- **T7.1.6** Integrate Cloudflare Analytics API
- **Estimate:** 3 days
- **Priority:** Medium
- **Dependencies:** T6.1

#### Task 7.2: Cloudflare Performance Optimization
- **T7.2.1** Implement Cloudflare Images optimization
- **T7.2.2** Add lazy loading cho components
- **T7.2.3** Optimize bundle size với Cloudflare Workers
- **T7.2.4** Implement Cloudflare Cache API
- **T7.2.5** Add Cloudflare Web Analytics
- **T7.2.6** Setup Cloudflare Speed Brain
- **Estimate:** 2 days
- **Priority:** Medium
- **Dependencies:** All previous tasks

### Sprint 8: Testing & Deployment (Week 8)

#### Task 8.1: Testing
- **T8.1.1** Write unit tests cho utilities
- **T8.1.2** Create integration tests cho API calls
- **T8.1.3** Implement E2E tests với Playwright
- **T8.1.4** Add component testing với Testing Library
- **T8.1.5** Performance testing với Lighthouse CI
- **Estimate:** 3 days
- **Priority:** High
- **Dependencies:** All features

#### Task 8.2: Cloudflare Deployment & DevOps
- **T8.2.1** Setup Cloudflare Pages deployment
- **T8.2.2** Configure Cloudflare environment variables
- **T8.2.3** Setup monitoring với Cloudflare Analytics
- **T8.2.4** Implement CI/CD pipeline với GitHub Actions
- **T8.2.5** Create deployment documentation
- **T8.2.6** Setup Cloudflare DNS và custom domain
- **Estimate:** 2 days
- **Priority:** High
- **Dependencies:** T8.1

### Additional Tasks (Ongoing)

#### Task A.1: Documentation
- **TA.1.1** API documentation
- **TA.1.2** Component documentation
- **TA.1.3** Deployment guide
- **TA.1.4** User manual
- **Estimate:** 1 day/week
- **Priority:** Medium

#### Task A.2: SEO & Marketing
- **TA.2.1** SEO optimization
- **TA.2.2** Meta tags implementation
- **TA.2.3** Sitemap generation
- **TA.2.4** Social media integration
- **Estimate:** 1 day/week
- **Priority:** Medium

### Risk Mitigation Tasks

#### Task R.1: Backup Plans
- **TR.1.1** Alternative API integration (nếu AccessTrade có vấn đề)
- **TR.1.2** Fallback UI components
- **TR.1.3** Error boundary implementation
- **TR.1.4** Data backup strategies
- **Estimate:** 2 days
- **Priority:** Low

### 📊 Optimization Summary

#### ✅ **Tasks đã GỘP LẠI:**
1. **Authentication System** (T1.2.5 + T6.1.1-T6.1.5) → Tiết kiệm 1 day
2. **Hono.dev Integration** (T1.4 + T2.1) → Tiết kiệm 2 days
3. **TanStack Table Setup** (T1.3.7 + T4.2.1 + T6.2.1) → Tiết kiệm 1 day
4. **Task 2.2 chia nhỏ** → Tăng 1 day nhưng dễ quản lý hơn
5. **Task 3.1 chia nhỏ** → Tăng 0.5 day nhưng dễ quản lý hơn

#### 🎯 **Kết quả tối ưu:**
- **Tổng tiết kiệm:** ~2.5 days
- **Loại bỏ:** 8 tasks trùng lặp
- **Chia nhỏ:** 2 tasks lớn thành 6 tasks nhỏ
- **Cải thiện:** Dependencies và task flow

#### 🔍 **Tasks cần REVIEW:**
- **T1.3.9:** Database Access Strategy (Drizzle vs MCP server)
- **T6.2.6:** Database operations strategy review
- **Dependencies:** Cập nhật theo cấu trúc mới

### Definition of Done

Mỗi task được coi là hoàn thành khi:
- [ ] Code được review và approve
- [ ] Unit tests pass (coverage > 80%)
- [ ] Integration tests pass
- [ ] Documentation được update
- [ ] Performance requirements đạt
- [ ] Security checklist completed
- [ ] Deployed to staging environment
- [ ] QA testing completed

/**
 * API Error Handling Utilities
 * 
 * Cung cấp comprehensive error handling cho:
 * - TanStack Query errors
 * - API response errors
 * - Network errors
 * - Validation errors
 * - Type-safe error handling
 */

import { z } from 'zod';

// ===== ERROR TYPES =====

/**
 * Base API Error interface
 */
export interface ApiError {
  message: string;
  code: string;
  status: number;
  details?: Record<string, any>;
  timestamp: string;
}

/**
 * Validation Error interface
 */
export interface ValidationError extends ApiError {
  code: 'VALIDATION_ERROR';
  fieldErrors: Record<string, string[]>;
}

/**
 * Network Error interface
 */
export interface NetworkError extends ApiError {
  code: 'NETWORK_ERROR';
  isOffline: boolean;
  retryable: boolean;
}

/**
 * Authentication Error interface
 */
export interface AuthError extends ApiError {
  code: 'AUTH_ERROR';
  redirectTo?: string;
}

/**
 * Server Error interface
 */
export interface ServerError extends ApiError {
  code: 'SERVER_ERROR';
  retryable: boolean;
}

/**
 * Union type cho tất cả error types
 */
export type AppError = ApiError | ValidationError | NetworkError | AuthError | ServerError;

// ===== ERROR SCHEMAS =====

/**
 * Zod schema cho API error response
 */
export const apiErrorSchema = z.object({
  message: z.string(),
  code: z.string(),
  status: z.number(),
  details: z.record(z.any()).optional(),
  timestamp: z.string(),
});

/**
 * Zod schema cho validation error
 */
export const validationErrorSchema = apiErrorSchema.extend({
  code: z.literal('VALIDATION_ERROR'),
  fieldErrors: z.record(z.array(z.string())),
});

// ===== ERROR CLASSES =====

/**
 * Custom Error class cho API errors
 */
export class ApiErrorClass extends Error {
  public readonly code: string;
  public readonly status: number;
  public readonly details?: Record<string, any>;
  public readonly timestamp: string;

  constructor(error: ApiError) {
    super(error.message);
    this.name = 'ApiError';
    this.code = error.code;
    this.status = error.status;
    this.details = error.details;
    this.timestamp = error.timestamp;
  }

  /**
   * Check if error is retryable
   */
  isRetryable(): boolean {
    // 5xx errors are generally retryable
    if (this.status >= 500) return true;
    
    // Specific retryable codes
    const retryableCodes = ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'RATE_LIMIT'];
    return retryableCodes.includes(this.code);
  }

  /**
   * Check if error requires authentication
   */
  requiresAuth(): boolean {
    return this.status === 401 || this.code === 'AUTH_ERROR';
  }

  /**
   * Convert to plain object
   */
  toJSON(): ApiError {
    return {
      message: this.message,
      code: this.code,
      status: this.status,
      details: this.details,
      timestamp: this.timestamp,
    };
  }
}

// ===== ERROR FACTORY FUNCTIONS =====

/**
 * Create validation error
 */
export function createValidationError(
  fieldErrors: Record<string, string[]>,
  message = 'Validation failed'
): ValidationError {
  return {
    message,
    code: 'VALIDATION_ERROR',
    status: 400,
    fieldErrors,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create network error
 */
export function createNetworkError(
  message = 'Network error occurred',
  isOffline = false
): NetworkError {
  return {
    message,
    code: 'NETWORK_ERROR',
    status: 0,
    isOffline,
    retryable: true,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create authentication error
 */
export function createAuthError(
  message = 'Authentication required',
  redirectTo?: string
): AuthError {
  return {
    message,
    code: 'AUTH_ERROR',
    status: 401,
    redirectTo,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Create server error
 */
export function createServerError(
  message = 'Internal server error',
  status = 500,
  retryable = true
): ServerError {
  return {
    message,
    code: 'SERVER_ERROR',
    status,
    retryable,
    timestamp: new Date().toISOString(),
  };
}

// ===== ERROR PARSING FUNCTIONS =====

/**
 * Parse error từ fetch response
 */
export async function parseApiError(response: Response): Promise<AppError> {
  try {
    const errorData = await response.json();
    
    // Validate error data với Zod
    const parsedError = apiErrorSchema.safeParse(errorData);
    
    if (parsedError.success) {
      return parsedError.data;
    }
    
    // Fallback nếu response không match schema
    return {
      message: errorData.message || response.statusText || 'Unknown error',
      code: 'API_ERROR',
      status: response.status,
      timestamp: new Date().toISOString(),
    };
  } catch {
    // Fallback nếu không parse được JSON
    return {
      message: response.statusText || 'Unknown error',
      code: 'API_ERROR',
      status: response.status,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Parse error từ unknown error object
 */
export function parseUnknownError(error: unknown): AppError {
  // Network error
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return createNetworkError('Network connection failed');
  }
  
  // API Error class
  if (error instanceof ApiErrorClass) {
    return error.toJSON();
  }
  
  // Standard Error
  if (error instanceof Error) {
    return {
      message: error.message,
      code: 'UNKNOWN_ERROR',
      status: 500,
      timestamp: new Date().toISOString(),
    };
  }
  
  // Unknown error type
  return {
    message: 'An unknown error occurred',
    code: 'UNKNOWN_ERROR',
    status: 500,
    details: { originalError: error },
    timestamp: new Date().toISOString(),
  };
}

// ===== ERROR HANDLING UTILITIES =====

/**
 * Enhanced fetch wrapper với error handling
 */
export async function apiRequest<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await parseApiError(response);
      throw new ApiErrorClass(error);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiErrorClass) {
      throw error;
    }
    
    const parsedError = parseUnknownError(error);
    throw new ApiErrorClass(parsedError);
  }
}

/**
 * Retry wrapper cho API calls
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      // Không retry nếu không phải retryable error
      if (error instanceof ApiErrorClass && !error.isRetryable()) {
        throw error;
      }
      
      // Không retry ở attempt cuối
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait trước khi retry với exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, delay * Math.pow(2, attempt))
      );
    }
  }
  
  throw lastError!;
}

/**
 * Error boundary helper cho React components
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof ApiErrorClass) {
    return error.message;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
}

/**
 * Check if error is specific type
 */
export function isValidationError(error: unknown): error is ValidationError {
  return error instanceof ApiErrorClass && error.code === 'VALIDATION_ERROR';
}

export function isNetworkError(error: unknown): error is NetworkError {
  return error instanceof ApiErrorClass && error.code === 'NETWORK_ERROR';
}

export function isAuthError(error: unknown): error is AuthError {
  return error instanceof ApiErrorClass && error.code === 'AUTH_ERROR';
}

export function isServerError(error: unknown): error is ServerError {
  return error instanceof ApiErrorClass && error.code === 'SERVER_ERROR';
}

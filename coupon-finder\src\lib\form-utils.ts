/**
 * TanStack Form Utilities với Zod v4 Integration
 * 
 * Cung cấp utilities để tích hợp TanStack Form với Zod v4 schemas
 * và type-safe form handling cho toàn bộ dự án
 */

import { useForm, type FormApi, type <PERSON><PERSON><PERSON>, type <PERSON>Field<PERSON>pi, type AnyForm<PERSON><PERSON> } from '@tanstack/react-form';
import { z } from 'zod/v4';
import type { ZodSchema, ZodError } from 'zod/v4';

// ===== TYPE DEFINITIONS =====

/**
 * Form validation result type
 */
export type ValidationResult = string | undefined;

/**
 * Form field error type
 */
export interface FieldError {
  message: string;
  path: string[];
}

/**
 * Form submission result
 */
export interface FormSubmissionResult<T = unknown> {
  success: boolean;
  data?: T;
  errors?: Record<string, string>;
  message?: string;
}

// ===== ZOD INTEGRATION UTILITIES =====

/**
 * Tạo validator function từ Zod schema cho TanStack Form
 * Sử dụng fieldApi.parseValueWithSchema() (TanStack Form feature)
 */
export function createZodValidator<T>(schema: ZodSchema<T>) {
  return ({ value, fieldApi }: { value: unknown; fieldApi: AnyFieldApi }): ValidationResult => {
    try {
      // Sử dụng TanStack Form's parseValueWithSchema method
      const errors = fieldApi.parseValueWithSchema(schema);
      return errors ? errors[0]?.message : undefined;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || 'Validation error';
      }
      return 'Unknown validation error';
    }
  };
}

/**
 * Tạo async validator function từ Zod schema
 */
export function createAsyncZodValidator<T>(schema: ZodSchema<T>) {
  return async ({ value, fieldApi }: { value: unknown; fieldApi: AnyFieldApi }): Promise<ValidationResult> => {
    try {
      await schema.parseAsync(value);
      return undefined;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return error.errors[0]?.message || 'Validation error';
      }
      return 'Unknown validation error';
    }
  };
}

/**
 * Tạo form-level validator từ Zod schema
 */
export function createFormZodValidator<T>(schema: ZodSchema<T>) {
  return ({ value }: { value: T }) => {
    try {
      schema.parse(value);
      return undefined;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          const path = err.path.join('.');
          errors[path] = err.message;
        });
        return { fields: errors };
      }
      return 'Form validation failed';
    }
  };
}

// ===== FORM HOOKS =====

/**
 * Custom hook để tạo type-safe form với Zod validation
 */
export function useZodForm<T extends Record<string, any>>(options: {
  schema: ZodSchema<T>;
  defaultValues: T;
  onSubmit: (data: T) => Promise<FormSubmissionResult<T>> | FormSubmissionResult<T>;
  onSubmitInvalid?: (errors: Record<string, string>) => void;
}) {
  const { schema, defaultValues, onSubmit, onSubmitInvalid } = options;

  const form = useForm({
    defaultValues,
    validators: {
      // Form-level validation với Zod schema
      onChange: createFormZodValidator(schema),
      onSubmit: createFormZodValidator(schema),
    },
    onSubmit: async ({ value }) => {
      try {
        // Validate toàn bộ form trước khi submit
        const validatedData = schema.parse(value);
        const result = await onSubmit(validatedData);
        
        if (!result.success && result.errors && onSubmitInvalid) {
          onSubmitInvalid(result.errors);
        }
        
        return result;
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errors: Record<string, string> = {};
          error.errors.forEach(err => {
            const path = err.path.join('.');
            errors[path] = err.message;
          });
          
          if (onSubmitInvalid) {
            onSubmitInvalid(errors);
          }
          
          return {
            success: false,
            errors,
            message: 'Form validation failed'
          };
        }
        
        return {
          success: false,
          message: 'Submission failed'
        };
      }
    },
  });

  return form;
}

// ===== FIELD UTILITIES =====

/**
 * Utility để tạo field validators cho các trường hợp phổ biến
 */
export const fieldValidators = {
  /**
   * Required field validator
   */
  required: (message = 'Trường này là bắt buộc') => 
    ({ value }: { value: any }): ValidationResult => {
      if (value === undefined || value === null || value === '') {
        return message;
      }
      return undefined;
    },

  /**
   * Email validator sử dụng Zod v4
   */
  email: (message = 'Email không hợp lệ') =>
    ({ value }: { value: string }): ValidationResult => {
      try {
        z.string().email().parse(value);
        return undefined;
      } catch {
        return message;
      }
    },

  /**
   * URL validator sử dụng Zod v4
   */
  url: (message = 'URL không hợp lệ') =>
    ({ value }: { value: string }): ValidationResult => {
      try {
        z.string().url().parse(value);
        return undefined;
      } catch {
        return message;
      }
    },

  /**
   * Min length validator
   */
  minLength: (min: number, message?: string) => 
    ({ value }: { value: string }): ValidationResult => {
      if (typeof value === 'string' && value.length < min) {
        return message || `Phải có ít nhất ${min} ký tự`;
      }
      return undefined;
    },

  /**
   * Max length validator
   */
  maxLength: (max: number, message?: string) => 
    ({ value }: { value: string }): ValidationResult => {
      if (typeof value === 'string' && value.length > max) {
        return message || `Không được quá ${max} ký tự`;
      }
      return undefined;
    },

  /**
   * Number range validator
   */
  numberRange: (min: number, max: number, message?: string) => 
    ({ value }: { value: number }): ValidationResult => {
      if (typeof value === 'number' && (value < min || value > max)) {
        return message || `Giá trị phải từ ${min} đến ${max}`;
      }
      return undefined;
    },

  /**
   * Pattern validator
   */
  pattern: (regex: RegExp, message = 'Định dạng không hợp lệ') => 
    ({ value }: { value: string }): ValidationResult => {
      if (typeof value === 'string' && !regex.test(value)) {
        return message;
      }
      return undefined;
    },

  /**
   * Custom async validator
   */
  asyncCustom: (
    validatorFn: (value: any) => Promise<boolean>,
    message = 'Validation failed'
  ) => 
    async ({ value }: { value: any }): Promise<ValidationResult> => {
      try {
        const isValid = await validatorFn(value);
        return isValid ? undefined : message;
      } catch {
        return message;
      }
    },
};

// ===== ERROR HANDLING UTILITIES =====

/**
 * Format form errors cho display
 */
export function formatFormErrors(form: AnyFormApi): Record<string, string> {
  const errors: Record<string, string> = {};
  
  // Get form-level errors
  const formErrors = form.state.errors;
  if (formErrors.length > 0) {
    errors._form = formErrors[0];
  }
  
  // Get field-level errors từ errorMap
  const errorMap = form.state.errorMap;
  Object.entries(errorMap).forEach(([event, error]) => {
    if (error && typeof error === 'object' && 'fields' in error) {
      Object.entries(error.fields as Record<string, string>).forEach(([field, message]) => {
        errors[field] = message;
      });
    } else if (error && typeof error === 'string') {
      errors[event] = error;
    }
  });
  
  return errors;
}

/**
 * Check if form has any errors
 */
export function hasFormErrors(form: AnyFormApi): boolean {
  return !form.state.isValid || form.state.errors.length > 0;
}

/**
 * Get first error message from form
 */
export function getFirstFormError(form: AnyFormApi): string | undefined {
  const errors = formatFormErrors(form);
  const errorKeys = Object.keys(errors);
  return errorKeys.length > 0 ? errors[errorKeys[0]] : undefined;
}

// ===== FORM STATE UTILITIES =====

/**
 * Check if form is submitting
 */
export function isFormSubmitting(form: AnyFormApi): boolean {
  return form.state.isSubmitting;
}

/**
 * Check if form can be submitted
 */
export function canSubmitForm(form: AnyFormApi): boolean {
  return form.state.isValid && !form.state.isSubmitting;
}

/**
 * Reset form to initial state
 */
export function resetForm(form: AnyFormApi): void {
  form.reset();
}

/**
 * Set form field value programmatically
 */
export function setFormFieldValue<T>(
  form: AnyFormApi,
  fieldName: string,
  value: T
): void {
  const field = form.getFieldInfo(fieldName);
  if (field) {
    form.setFieldValue(fieldName, value);
  }
}

// ===== DEBOUNCE UTILITIES =====

/**
 * Debounce function cho async validation
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Default debounce time cho async validation (ms)
 */
export const DEFAULT_ASYNC_DEBOUNCE_MS = 300;

/**
 * Environment variables configuration and validation
 * Provides type-safe access to environment variables
 */

// Cloudflare Workers Environment with KV namespaces
export interface CloudflareEnv {
  // KV Namespaces
  CACHE: KVNamespace;
  ANALYTICS: KVNamespace;
  CONFIG: KVNamespace;

  // D1 Database
  DB: D1Database;

  // Environment variables
  NODE_ENV: string;
  APP_NAME: string;
  APP_URL: string;
  APP_VERSION: string;
  ACCESSTRADE_API_KEY: string;
  ACCESSTRADE_TOKEN: string;
  JWT_SECRET: string;
  ENCRYPTION_KEY: string;
  ENABLE_ANALYTICS: string;
  ENABLE_CACHING: string;
  ENABLE_DEBUG: string;
  RATE_LIMIT_REQUESTS: string;
  RATE_LIMIT_WINDOW: string;
  LOG_LEVEL: string;
  LOG_FORMAT: string;
}

// Environment variable schema
export interface EnvConfig {
  // Application
  NODE_ENV: 'development' | 'production' | 'test';
  APP_NAME: string;
  APP_URL: string;
  APP_VERSION: string;

  // API
  API_BASE_URL: string;
  API_TIMEOUT: number;

  // AccessTrade
  ACCESSTRADE_API_URL: string;
  ACCESSTRADE_API_KEY: string;
  ACCESSTRADE_TOKEN: string;

  // Cloudflare
  CLOUDFLARE_ACCOUNT_ID: string;
  CLOUDFLARE_API_TOKEN: string;

  // Database (D1)
  DATABASE_URL: string;
  DATABASE_ID: string;
  CLOUDFLARE_DATABASE_ID: string;
  CLOUDFLARE_D1_TOKEN: string;

  // Cache
  KV_NAMESPACE_ID: string;
  CACHE_TTL: number;

  // Analytics
  ANALYTICS_ID: string;
  GOOGLE_ANALYTICS_ID: string;

  // Security
  JWT_SECRET: string;
  ENCRYPTION_KEY: string;

  // Feature Flags
  ENABLE_ANALYTICS: boolean;
  ENABLE_CACHING: boolean;
  ENABLE_DEBUG: boolean;

  // Rate Limiting
  RATE_LIMIT_REQUESTS: number;
  RATE_LIMIT_WINDOW: number;

  // Logging
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
  LOG_FORMAT: 'json' | 'text';
}

/**
 * Get environment variable with type conversion
 */
function getEnvVar(key: string, defaultValue?: string): string {
  // In TanStack Start, we can access env vars from different sources
  const value =
    // Server-side (Cloudflare Workers)
    (typeof process !== 'undefined' && process.env?.[key]) ||
    // Client-side (import.meta.env)
    (typeof import.meta !== 'undefined' && import.meta.env?.[key]) ||
    // Fallback to default
    defaultValue;

  if (value === undefined) {
    throw new Error(`Environment variable ${key} is not defined`);
  }

  return value;
}

/**
 * Get environment variable as number
 */
function getEnvNumber(key: string, defaultValue?: number): number {
  const value = getEnvVar(key, defaultValue?.toString());
  const parsed = parseInt(value, 10);

  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${key} must be a valid number`);
  }

  return parsed;
}

/**
 * Get environment variable as boolean
 */
function getEnvBoolean(key: string, defaultValue?: boolean): boolean {
  const value = getEnvVar(key, defaultValue?.toString());
  return value.toLowerCase() === 'true';
}

/**
 * Validate and parse environment variables
 */
export function createEnvConfig(): EnvConfig {
  return {
    // Application
    NODE_ENV: getEnvVar('NODE_ENV', 'development') as EnvConfig['NODE_ENV'],
    APP_NAME: getEnvVar('APP_NAME', 'Coupon Finder'),
    APP_URL: getEnvVar('APP_URL', 'http://localhost:3000'),
    APP_VERSION: getEnvVar('APP_VERSION', '1.0.0'),

    // API
    API_BASE_URL: getEnvVar('API_BASE_URL', 'http://localhost:3000/api'),
    API_TIMEOUT: getEnvNumber('API_TIMEOUT', 30000),

    // AccessTrade
    ACCESSTRADE_API_URL: getEnvVar(
      'ACCESSTRADE_API_URL',
      'https://api.accesstrade.vn'
    ),
    ACCESSTRADE_API_KEY: getEnvVar('ACCESSTRADE_API_KEY', ''),
    ACCESSTRADE_TOKEN: getEnvVar('ACCESSTRADE_TOKEN', ''),

    // Cloudflare
    CLOUDFLARE_ACCOUNT_ID: getEnvVar('CLOUDFLARE_ACCOUNT_ID', ''),
    CLOUDFLARE_API_TOKEN: getEnvVar('CLOUDFLARE_API_TOKEN', ''),

    // Database (D1)
    DATABASE_URL: getEnvVar('DATABASE_URL', ''),
    DATABASE_ID: getEnvVar('DATABASE_ID', ''),
    CLOUDFLARE_DATABASE_ID: getEnvVar('CLOUDFLARE_DATABASE_ID', ''),
    CLOUDFLARE_D1_TOKEN: getEnvVar('CLOUDFLARE_D1_TOKEN', ''),

    // Cache
    KV_NAMESPACE_ID: getEnvVar('KV_NAMESPACE_ID', ''),
    CACHE_TTL: getEnvNumber('CACHE_TTL', 3600),

    // Analytics
    ANALYTICS_ID: getEnvVar('ANALYTICS_ID', ''),
    GOOGLE_ANALYTICS_ID: getEnvVar('GOOGLE_ANALYTICS_ID', ''),

    // Security
    JWT_SECRET: getEnvVar('JWT_SECRET', ''),
    ENCRYPTION_KEY: getEnvVar('ENCRYPTION_KEY', ''),

    // Feature Flags
    ENABLE_ANALYTICS: getEnvBoolean('ENABLE_ANALYTICS', true),
    ENABLE_CACHING: getEnvBoolean('ENABLE_CACHING', true),
    ENABLE_DEBUG: getEnvBoolean('ENABLE_DEBUG', false),

    // Rate Limiting
    RATE_LIMIT_REQUESTS: getEnvNumber('RATE_LIMIT_REQUESTS', 100),
    RATE_LIMIT_WINDOW: getEnvNumber('RATE_LIMIT_WINDOW', 900000),

    // Logging
    LOG_LEVEL: getEnvVar('LOG_LEVEL', 'info') as EnvConfig['LOG_LEVEL'],
    LOG_FORMAT: getEnvVar('LOG_FORMAT', 'json') as EnvConfig['LOG_FORMAT'],
  };
}

// Global environment configuration
export const env = createEnvConfig();

// Helper functions for common checks
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

// Validation helper
export function validateRequiredEnvVars(): void {
  const requiredVars = [
    'ACCESSTRADE_API_KEY',
    'ACCESSTRADE_TOKEN',
    'JWT_SECRET',
  ];

  const missing = requiredVars.filter(key => !getEnvVar(key, ''));

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}`
    );
  }
}

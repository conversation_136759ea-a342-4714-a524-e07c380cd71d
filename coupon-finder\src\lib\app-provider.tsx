import * as React from 'react';
import { QueryProvider } from './query-provider';
import { StoreProvider } from '@/stores/store-provider';
import { FormProvider } from './form-provider';
import { AuthProvider } from './auth-context';

/**
 * AppProvider - Gộp tất cả providers thành một component duy nhất
 *
 * Hierarchy:
 * 1. QueryProvider (TanStack Query) - Server state management
 * 2. StoreProvider (Zustand) - Client state management
 * 3. AuthProvider (Auth Context) - Authentication state management
 * 4. FormProvider (TanStack Form) - Form state management với Zod v4 validation
 *
 * Lợi ích:
 * - Single provider component để wrap app
 * - Proper provider hierarchy
 * - Tối ưu performance
 * - Dễ maintain và debug
 * - Type-safe form validation với Zod v4
 */

interface AppProviderProps {
  children: React.ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  return (
    <QueryProvider>
      <StoreProvider>
        <AuthProvider>
          <FormProvider>
            {children}
          </FormProvider>
        </AuthProvider>
      </StoreProvider>
    </QueryProvider>
  );
}

/**
 * Hook để check if app providers are ready
 */
export function useAppReady() {
  // Providers are always ready với current setup
  return true;
}

/**
 * Hook để access provider context information
 */
export function useProviderInfo() {
  return {
    hasQueryProvider: true,
    hasStoreProvider: true,
    hasAuthProvider: true,
    hasFormProvider: true,
    isReady: true,
  };
}
